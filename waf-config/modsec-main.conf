# 🛡️ Configuration ModSecurity pour Retreat And Be
# Inclut les règles OWASP Core Rule Set

# Charger les règles de base
Include /etc/nginx/modsec/modsecurity.conf
Include /opt/owasp-modsecurity-crs/crs-setup.conf
Include /opt/owasp-modsecurity-crs/rules/*.conf

# Règles personnalisées pour Retreat And Be
SecRule ARGS "@detectSQLi" \
    "id:1001,\
    phase:2,\
    block,\
    msg:'SQL Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-sqli'"

# Protection contre XSS
SecRule ARGS "@detectXSS" \
    "id:1002,\
    phase:2,\
    block,\
    msg:'XSS Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss'"

# Limitation de la taille des uploads
SecRule FILES_TMPNAMES "@inspectFile /opt/modsecurity/inspect_file.lua" \
    "id:1003,\
    phase:2,\
    t:none,\
    block,\
    msg:'Malicious file upload detected'"
