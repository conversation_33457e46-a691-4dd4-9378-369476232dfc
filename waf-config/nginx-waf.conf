# 🛡️ Configuration Nginx avec ModSecurity WAF
# Généré automatiquement pour Retreat And Be

# Configuration principale
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Charger le module ModSecurity
load_module modules/ngx_http_modsecurity_module.so;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Configuration des logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Configuration de performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Configuration de sécurité de base
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Configuration ModSecurity
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsec/main.conf;

    # Configuration de limitation de taux
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Serveur principal
    server {
        listen 80;
        listen [::]:80;
        server_name localhost retreat-and-be.local;

        # Redirection HTTPS (en production)
        # return 301 https://$server_name$request_uri;

        # Configuration ModSecurity pour ce serveur
        modsecurity on;

        # Protection contre les attaques de force brute
        location /api/auth/login {
            limit_req zone=login burst=3 nodelay;
            proxy_pass http://backend;
        }

        # Protection API avec limitation de taux
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            # Headers de sécurité
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_pass http://backend;
        }

        # Servir les fichiers statiques
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # Bloquer l'accès aux fichiers sensibles
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ \.(env|config|ini|log|bak)$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }

    # Configuration upstream pour le backend
    upstream backend {
        server 127.0.0.1:3000;
        keepalive 32;
    }
}
