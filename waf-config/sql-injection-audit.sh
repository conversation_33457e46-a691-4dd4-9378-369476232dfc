#!/bin/bash

# 🔍 AUDIT DES VULNÉRABILITÉS SQL INJECTION
# Recherche les patterns dangereux dans le code

PROJECT_ROOT="/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2"
AUDIT_REPORT="$1"

echo "# 🔍 AUDIT SQL INJECTION - $(date)" > "$AUDIT_REPORT"
echo "" >> "$AUDIT_REPORT"

# Patterns dangereux à rechercher
DANGEROUS_PATTERNS=(
    "SELECT.*\+.*"
    "INSERT.*\+.*"
    "UPDATE.*\+.*"
    "DELETE.*\+.*"
    "query\(.*\+.*\)"
    "execute\(.*\+.*\)"
    "\$\{.*\}.*SELECT"
    "template.*SELECT"
)

echo "## Patterns SQL dangereux détectés:" >> "$AUDIT_REPORT"
echo "" >> "$AUDIT_REPORT"

TOTAL_ISSUES=0

for pattern in "${DANGEROUS_PATTERNS[@]}"; do
    echo "### Pattern: $pattern" >> "$AUDIT_REPORT"
    echo "\`\`\`" >> "$AUDIT_REPORT"
    
    MATCHES=$(grep -r -n -E "$pattern" \
        --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" \
        --exclude-dir=node_modules --exclude-dir=.git --exclude-dir=dist \
        "$PROJECT_ROOT" 2>/dev/null || true)
    
    if [[ -n "$MATCHES" ]]; then
        echo "$MATCHES" >> "$AUDIT_REPORT"
        ISSUE_COUNT=$(echo "$MATCHES" | wc -l)
        TOTAL_ISSUES=$((TOTAL_ISSUES + ISSUE_COUNT))
    else
        echo "Aucune occurrence trouvée" >> "$AUDIT_REPORT"
    fi
    
    echo "\`\`\`" >> "$AUDIT_REPORT"
    echo "" >> "$AUDIT_REPORT"
done

echo "## Résumé" >> "$AUDIT_REPORT"
echo "- **Total des problèmes potentiels**: $TOTAL_ISSUES" >> "$AUDIT_REPORT"
echo "- **Recommandation**: Utiliser des requêtes préparées et ORM" >> "$AUDIT_REPORT"
