#!/bin/bash

# 🚀 DÉPLOIEMENT SÉCURISÉ AVEC WAF
# Script de déploiement pour l'environnement sécurisé

set -e

PROJECT_ROOT="/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2"
WAF_DIR="$PROJECT_ROOT/waf-config"

echo "🚀 Déploiement sécurisé avec WAF..."

# Vérifier que Vault est démarré
if ! curl -s http://localhost:8200/v1/sys/health > /dev/null; then
    echo "❌ Vault n'est pas accessible. Démarrez-le d'abord."
    exit 1
fi

# Charger les secrets
source "$PROJECT_ROOT/scripts/load-vault-secrets.sh"

# Créer les secrets Docker
echo "$DATABASE_URL" | docker secret create db_password - 2>/dev/null || true

# Démarrer les services
cd "$PROJECT_ROOT"
docker-compose -f "$WAF_DIR/docker-compose-waf.yml" up -d

echo "✅ Déploiement sécurisé terminé"
echo "🌐 Application accessible sur: http://localhost"
echo "🔒 WAF actif avec protection SQL injection et XSS"
