#!/bin/bash

# 🧪 TESTS DE SÉCURITÉ WAF ET SQL INJECTION
# Tests automatisés pour valider les protections

echo "🧪 Tests de sécurité WAF..."

BASE_URL="http://localhost"
TEST_RESULTS="/tmp/security-test-results.txt"

echo "# Tests de sécurité - $(date)" > "$TEST_RESULTS"

# Test 1: SQL Injection
echo "🔍 Test 1: Protection SQL Injection"
SQL_PAYLOAD="' OR '1'='1"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/users?id=$SQL_PAYLOAD" || echo "000")

if [[ "$RESPONSE" == "403" ]] || [[ "$RESPONSE" == "406" ]]; then
    echo "✅ SQL Injection bloquée (HTTP $RESPONSE)"
    echo "✅ Test SQL Injection: PASSÉ" >> "$TEST_RESULTS"
else
    echo "❌ SQL Injection non bloquée (HTTP $RESPONSE)"
    echo "❌ Test SQL Injection: ÉCHEC" >> "$TEST_RESULTS"
fi

# Test 2: XSS
echo "🔍 Test 2: Protection XSS"
XSS_PAYLOAD="<script>alert('xss')</script>"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/search?q=$XSS_PAYLOAD" || echo "000")

if [[ "$RESPONSE" == "403" ]] || [[ "$RESPONSE" == "406" ]]; then
    echo "✅ XSS bloqué (HTTP $RESPONSE)"
    echo "✅ Test XSS: PASSÉ" >> "$TEST_RESULTS"
else
    echo "❌ XSS non bloqué (HTTP $RESPONSE)"
    echo "❌ Test XSS: ÉCHEC" >> "$TEST_RESULTS"
fi

# Test 3: Limitation de taux
echo "🔍 Test 3: Limitation de taux"
for i in {1..15}; do
    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/test" || echo "000")
    if [[ "$RESPONSE" == "429" ]]; then
        echo "✅ Limitation de taux activée après $i requêtes"
        echo "✅ Test limitation de taux: PASSÉ" >> "$TEST_RESULTS"
        break
    fi
done

echo ""
echo "📋 Résultats des tests sauvegardés dans: $TEST_RESULTS"
cat "$TEST_RESULTS"
