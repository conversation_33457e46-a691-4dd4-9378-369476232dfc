# 🛡️ Docker Compose avec WAF intégré
# Configuration sécurisée pour Retreat And Be

version: '3.8'

services:
  # Nginx avec ModSecurity WAF
  nginx-waf:
    image: owasp/modsecurity-nginx:latest
    container_name: retreat-nginx-waf
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./waf-config/nginx-waf.conf:/etc/nginx/nginx.conf:ro
      - ./waf-config/modsec-main.conf:/etc/nginx/modsec/main.conf:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
    networks:
      - retreat-network
    restart: unless-stopped
    environment:
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx

  # Backend avec protection renforcée
  backend:
    build:
      context: ./Projet-RB2/Backend-NestJS
      dockerfile: Dockerfile
    container_name: retreat-backend-secure
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=${VAULT_TOKEN}
    volumes:
      - ./scripts/load-vault-secrets.sh:/app/load-secrets.sh:ro
    depends_on:
      - vault
      - postgres
      - redis
    networks:
      - retreat-network
    restart: unless-stopped
    command: >
      sh -c "source /app/load-secrets.sh && npm start"

  # HashiCorp Vault
  vault:
    image: vault:latest
    container_name: retreat-vault
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/data
      - ./vault-config:/vault/config:ro
    environment:
      - VAULT_ADDR=http://0.0.0.0:8200
    networks:
      - retreat-network
    cap_add:
      - IPC_LOCK
    restart: unless-stopped

  # Base de données PostgreSQL
  postgres:
    image: postgres:13
    container_name: retreat-postgres-secure
    environment:
      - POSTGRES_DB=retreat_and_be
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - retreat-network
    secrets:
      - db_password
    restart: unless-stopped

  # Redis
  redis:
    image: redis:alpine
    container_name: retreat-redis-secure
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - retreat-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  vault_data:

networks:
  retreat-network:
    driver: bridge

secrets:
  db_password:
    external: true
