# 📚 RUNBOOKS OPÉRATIONNELS EXCELLENCE 10/10
## Procédures d'Incident et Maintenance - Retreat And Be

**Version** : 1.0  
**Der<PERSON><PERSON> mise à jour** : 28 Mai 2025  
**Équipe** : DevOps & SRE

---

## 🚨 PROCÉDURES D'URGENCE

### 🔴 INCIDENT CRITIQUE - Service Indisponible

#### Symptômes
- Alertes `ServiceDown` ou `HealthCheckFailing`
- Erreurs 5xx > 50%
- Temps de réponse > 10 secondes

#### Actions Immédiates (< 5 minutes)
```bash
# 1. Vérification rapide du statut
kubectl get pods -n production
kubectl get services -n production
curl -I https://api.retreat-and-be.com/health

# 2. Logs d'erreur
kubectl logs -f deployment/retreat-and-be-backend -n production --tail=100

# 3. Rollback d'urgence si déploiement récent
kubectl rollout undo deployment/retreat-and-be-backend -n production
kubectl rollout status deployment/retreat-and-be-backend -n production
```

#### Investigation (< 15 minutes)
```bash
# Métriques système
kubectl top pods -n production
kubectl describe pod <pod-name> -n production

# Base de données
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -c "SELECT COUNT(*) FROM pg_stat_activity;"

# Redis
redis-cli -h $REDIS_HOST ping
redis-cli -h $REDIS_HOST info memory
```

#### Escalade
- **< 5 min** : Notification équipe DevOps
- **< 15 min** : Notification CTO
- **< 30 min** : Communication clients

---

### 🟡 INCIDENT MAJEUR - Performance Dégradée

#### Symptômes
- Temps de réponse P95 > 150ms
- Throughput < 2000 req/sec
- Score excellence < 9/10

#### Diagnostic Performance
```bash
# 1. Métriques applicatives
curl https://api.retreat-and-be.com/metrics | grep http_request_duration

# 2. Analyse base de données
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
WHERE mean_time > 100 
ORDER BY mean_time DESC LIMIT 10;"

# 3. Cache Redis
redis-cli -h $REDIS_HOST info stats | grep keyspace
```

#### Actions Correctives
```bash
# 1. Scaling horizontal immédiat
kubectl scale deployment retreat-and-be-backend --replicas=10 -n production

# 2. Optimisation cache
redis-cli -h $REDIS_HOST flushdb  # Si corruption suspectée
./scripts/warm-cache.sh

# 3. Optimisation DB
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -f database/performance-optimizations.sql
```

---

## 🔧 PROCÉDURES DE MAINTENANCE

### 📅 Maintenance Programmée

#### Pré-maintenance (J-1)
```bash
# 1. Backup complet
./scripts/backup-production.sh

# 2. Tests en staging
./scripts/deploy-staging.sh
./scripts/run-smoke-tests.sh

# 3. Communication
./scripts/notify-maintenance.sh
```

#### Pendant la maintenance
```bash
# 1. Mode maintenance
kubectl apply -f k8s/maintenance-mode.yaml

# 2. Déploiement
./scripts/deploy-production.sh

# 3. Validation
./scripts/validate-deployment.sh
./scripts/validate-excellence-10-10.sh
```

#### Post-maintenance
```bash
# 1. Désactivation mode maintenance
kubectl delete -f k8s/maintenance-mode.yaml

# 2. Monitoring renforcé
./scripts/enable-enhanced-monitoring.sh

# 3. Rapport
./scripts/generate-maintenance-report.sh
```

---

### 🗄️ Gestion Base de Données

#### Backup Quotidien
```bash
#!/bin/bash
# Script: /scripts/daily-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/postgresql"

# Backup avec compression
pg_dump -h $DB_HOST -U $DB_USER -d retreat_and_be \
  --verbose --format=custom --compress=9 \
  --file="$BACKUP_DIR/retreat_and_be_$DATE.dump"

# Vérification
pg_restore --list "$BACKUP_DIR/retreat_and_be_$DATE.dump" > /dev/null

# Nettoyage (garder 30 jours)
find $BACKUP_DIR -name "*.dump" -mtime +30 -delete

# Notification
echo "Backup completed: retreat_and_be_$DATE.dump" | \
  mail -s "DB Backup Success" <EMAIL>
```

#### Optimisation Hebdomadaire
```bash
#!/bin/bash
# Script: /scripts/weekly-optimization.sh

# Analyse et vacuum
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -c "
VACUUM ANALYZE;
REINDEX DATABASE retreat_and_be;
"

# Nettoyage données obsolètes
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -c "
SELECT cleanup_old_data();
"

# Rafraîchissement vues matérialisées
psql -h $DB_HOST -U $DB_USER -d retreat_and_be -c "
SELECT refresh_materialized_views();
"
```

---

### 🔄 Gestion Cache Redis

#### Monitoring Cache
```bash
#!/bin/bash
# Script: /scripts/monitor-redis.sh

# Métriques clés
redis-cli -h $REDIS_HOST info stats | grep -E "(keyspace_hits|keyspace_misses|used_memory)"

# Calcul hit ratio
HITS=$(redis-cli -h $REDIS_HOST info stats | grep keyspace_hits | cut -d: -f2)
MISSES=$(redis-cli -h $REDIS_HOST info stats | grep keyspace_misses | cut -d: -f2)
TOTAL=$((HITS + MISSES))
HIT_RATIO=$(echo "scale=2; $HITS * 100 / $TOTAL" | bc)

echo "Cache Hit Ratio: $HIT_RATIO%"

# Alerte si < 95%
if (( $(echo "$HIT_RATIO < 95" | bc -l) )); then
  echo "ALERT: Cache hit ratio below 95%" | \
    mail -s "Redis Performance Alert" <EMAIL>
fi
```

#### Réchauffement Cache
```bash
#!/bin/bash
# Script: /scripts/warm-cache.sh

# Activités populaires
curl -s "https://api.retreat-and-be.com/activities/popular" > /dev/null

# Catégories
for category in yoga meditation pilates massage; do
  curl -s "https://api.retreat-and-be.com/activities?category=$category" > /dev/null
done

# Localisations principales
for city in paris lyon marseille toulouse; do
  curl -s "https://api.retreat-and-be.com/activities?location=$city" > /dev/null
done

echo "Cache warming completed"
```

---

## 📊 PROCÉDURES DE MONITORING

### 🎯 Validation Excellence 10/10

#### Script de Validation Automatique
```bash
#!/bin/bash
# Script: /scripts/validate-excellence.sh

SCORE=0
MAX_SCORE=100

# Test 1: Performance (25 points)
RESPONSE_TIME=$(curl -w "%{time_total}" -s -o /dev/null https://api.retreat-and-be.com/health)
if (( $(echo "$RESPONSE_TIME < 0.15" | bc -l) )); then
  SCORE=$((SCORE + 25))
  echo "✅ Performance: ${RESPONSE_TIME}s (25 pts)"
else
  echo "❌ Performance: ${RESPONSE_TIME}s (0 pts)"
fi

# Test 2: Throughput (25 points)
THROUGHPUT=$(curl -s https://api.retreat-and-be.com/metrics | grep http_requests_total | tail -1 | awk '{print $2}')
if (( $(echo "$THROUGHPUT > 2000" | bc -l) )); then
  SCORE=$((SCORE + 25))
  echo "✅ Throughput: ${THROUGHPUT} req/s (25 pts)"
else
  echo "❌ Throughput: ${THROUGHPUT} req/s (0 pts)"
fi

# Test 3: Disponibilité (25 points)
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://api.retreat-and-be.com/health)
if [ "$HTTP_STATUS" = "200" ]; then
  SCORE=$((SCORE + 25))
  echo "✅ Disponibilité: $HTTP_STATUS (25 pts)"
else
  echo "❌ Disponibilité: $HTTP_STATUS (0 pts)"
fi

# Test 4: Cache (25 points)
HIT_RATIO=$(redis-cli -h $REDIS_HOST info stats | grep keyspace_hits | cut -d: -f2)
MISS_RATIO=$(redis-cli -h $REDIS_HOST info stats | grep keyspace_misses | cut -d: -f2)
CACHE_RATIO=$(echo "scale=2; $HIT_RATIO * 100 / ($HIT_RATIO + $MISS_RATIO)" | bc)

if (( $(echo "$CACHE_RATIO > 95" | bc -l) )); then
  SCORE=$((SCORE + 25))
  echo "✅ Cache: ${CACHE_RATIO}% (25 pts)"
else
  echo "❌ Cache: ${CACHE_RATIO}% (0 pts)"
fi

# Score final
FINAL_SCORE=$(echo "scale=1; $SCORE / 10" | bc)
echo ""
echo "🏆 SCORE EXCELLENCE: $FINAL_SCORE/10"

if (( $(echo "$FINAL_SCORE >= 10" | bc -l) )); then
  echo "🎉 EXCELLENCE 10/10 ATTEINTE!"
else
  echo "📈 Améliorations nécessaires: $((100 - SCORE)) points manquants"
fi
```

---

### 📈 Rapports Automatiques

#### Rapport Quotidien
```bash
#!/bin/bash
# Script: /scripts/daily-report.sh

DATE=$(date +%Y-%m-%d)
REPORT_FILE="/reports/daily_report_$DATE.md"

cat > $REPORT_FILE << EOF
# 📊 RAPPORT QUOTIDIEN - $DATE

## Métriques Excellence 10/10
- **Performance P95**: $(./scripts/get-metric.sh response_time_p95)ms
- **Throughput**: $(./scripts/get-metric.sh throughput) req/s
- **Disponibilité**: $(./scripts/get-metric.sh uptime)%
- **Erreurs**: $(./scripts/get-metric.sh error_rate)%

## Métriques Business
- **Nouvelles réservations**: $(./scripts/get-metric.sh bookings_today)
- **Revenus**: $(./scripts/get-metric.sh revenue_today)€
- **Nouveaux utilisateurs**: $(./scripts/get-metric.sh users_today)

## Incidents
$(./scripts/get-incidents.sh today)

## Actions Requises
$(./scripts/get-action-items.sh)
EOF

# Envoi par email
mail -s "Rapport Quotidien $DATE" -a $REPORT_FILE <EMAIL> < $REPORT_FILE
```

---

## 🔐 PROCÉDURES DE SÉCURITÉ

### 🛡️ Incident de Sécurité

#### Détection Vulnérabilité Critique
```bash
# 1. Isolation immédiate
kubectl scale deployment retreat-and-be-backend --replicas=0 -n production

# 2. Analyse forensique
kubectl logs deployment/retreat-and-be-backend -n production > /security/incident_logs_$(date +%Y%m%d_%H%M%S).log

# 3. Patch d'urgence
./scripts/emergency-security-patch.sh

# 4. Redéploiement sécurisé
./scripts/secure-redeploy.sh
```

#### Audit de Sécurité Hebdomadaire
```bash
#!/bin/bash
# Script: /scripts/weekly-security-audit.sh

# Scan des vulnérabilités
trivy image retreat-and-be:latest --severity HIGH,CRITICAL

# Audit des dépendances
npm audit --audit-level high
pip-audit

# Vérification des certificats
openssl s_client -connect retreat-and-be.com:443 -servername retreat-and-be.com | \
  openssl x509 -noout -dates

# Rapport de sécurité
./scripts/generate-security-report.sh
```

---

## 📞 CONTACTS D'URGENCE

### Équipe DevOps
- **DevOps Lead**: +33 6 XX XX XX XX
- **SRE Engineer**: +33 6 XX XX XX XX
- **Security Engineer**: +33 6 XX XX XX XX

### Escalade
- **CTO**: +33 6 XX XX XX XX
- **CEO**: +33 6 XX XX XX XX

### Fournisseurs
- **Cloud Provider**: Support 24/7
- **CDN Provider**: Support technique
- **Monitoring**: Support premium

---

## 📋 CHECKLIST DE VALIDATION

### ✅ Avant Déploiement
- [ ] Tests automatisés passants
- [ ] Validation sécurité
- [ ] Backup effectué
- [ ] Rollback plan prêt
- [ ] Équipe notifiée

### ✅ Après Déploiement
- [ ] Health checks OK
- [ ] Métriques normales
- [ ] Score excellence 10/10
- [ ] Monitoring actif
- [ ] Documentation mise à jour

### ✅ Excellence 10/10
- [ ] Performance P95 < 150ms
- [ ] Throughput > 2000 req/s
- [ ] Disponibilité > 99.9%
- [ ] Erreurs < 1%
- [ ] Cache hit > 95%

---

**🎯 OBJECTIF : MAINTENIR L'EXCELLENCE 10/10 EN PERMANENCE**

*Runbooks mis à jour le 28 Mai 2025*  
*Version 1.0 - Excellence Opérationnelle*
