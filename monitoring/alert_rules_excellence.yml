groups:
  - name: retreat-and-be-excellence-alerts
    interval: 30s
    rules:
      # =====================================================
      # ALERTES CRITIQUES POUR EXCELLENCE 10/10
      # =====================================================
      
      # Performance - Temps de réponse
      - alert: HighResponseTimeP95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.15
        for: 2m
        labels:
          severity: critical
          team: backend
          excellence_criteria: performance
        annotations:
          summary: "Temps de réponse P95 élevé détecté"
          description: "Le temps de réponse P95 est de {{ $value }}s, supérieur à la cible de 150ms pour l'excellence 10/10"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/high-response-time"
          dashboard_url: "https://grafana.retreat-and-be.com/d/performance"

      - alert: HighResponseTimeP99
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 1m
        labels:
          severity: warning
          team: backend
          excellence_criteria: performance
        annotations:
          summary: "Temps de réponse P99 élevé"
          description: "Le temps de réponse P99 est de {{ $value }}s, supérieur à 500ms"

      # Throughput - Débit de requêtes
      - alert: LowThroughput
        expr: rate(http_requests_total[5m]) < 2000
        for: 3m
        labels:
          severity: critical
          team: backend
          excellence_criteria: throughput
        annotations:
          summary: "Débit de requêtes insuffisant pour excellence 10/10"
          description: "Le débit actuel est de {{ $value }} req/sec, inférieur à la cible de 2000 req/sec"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/low-throughput"

      # Taux d'erreur
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
        for: 1m
        labels:
          severity: critical
          team: backend
          excellence_criteria: reliability
        annotations:
          summary: "Taux d'erreur élevé détecté"
          description: "Le taux d'erreur 5xx est de {{ $value | humanizePercentage }}, supérieur à 1%"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/high-error-rate"

      - alert: HighErrorRate4xx
        expr: rate(http_requests_total{status=~"4.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
          team: backend
          excellence_criteria: reliability
        annotations:
          summary: "Taux d'erreur 4xx élevé"
          description: "Le taux d'erreur 4xx est de {{ $value | humanizePercentage }}, supérieur à 5%"

      # =====================================================
      # ALERTES INFRASTRUCTURE
      # =====================================================

      # Base de données
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
          team: backend
          component: database
        annotations:
          summary: "Nombre élevé de connexions à la base de données"
          description: "{{ $value }} connexions actives, supérieur à 80"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/database-connections"

      - alert: DatabaseSlowQueries
        expr: pg_stat_statements_mean_time_ms > 1000
        for: 3m
        labels:
          severity: critical
          team: backend
          component: database
        annotations:
          summary: "Requêtes lentes détectées en base de données"
          description: "Temps moyen des requêtes: {{ $value }}ms, supérieur à 1000ms"

      # Redis Cache
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: critical
          team: backend
          component: cache
        annotations:
          summary: "Utilisation mémoire Redis élevée"
          description: "Redis utilise {{ $value | humanizePercentage }} de sa mémoire allouée"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/redis-memory"

      - alert: RedisCacheHitRateLow
        expr: redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) < 0.95
        for: 10m
        labels:
          severity: warning
          team: backend
          component: cache
          excellence_criteria: performance
        annotations:
          summary: "Taux de hit cache Redis faible"
          description: "Taux de hit: {{ $value | humanizePercentage }}, inférieur à 95% requis pour l'excellence"

      # Kubernetes
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: critical
          team: devops
          component: kubernetes
        annotations:
          summary: "Pod en crash loop détecté"
          description: "Le pod {{ $labels.pod }} redémarre fréquemment"

      - alert: PodMemoryUsageHigh
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          team: devops
          component: kubernetes
        annotations:
          summary: "Utilisation mémoire pod élevée"
          description: "Le pod {{ $labels.pod }} utilise {{ $value | humanizePercentage }} de sa mémoire"

      - alert: PodCPUUsageHigh
        expr: rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota * 100 > 80
        for: 10m
        labels:
          severity: warning
          team: devops
          component: kubernetes
        annotations:
          summary: "Utilisation CPU pod élevée"
          description: "Le pod {{ $labels.pod }} utilise {{ $value }}% de son CPU"

      # =====================================================
      # ALERTES BUSINESS
      # =====================================================

      # Réservations
      - alert: BookingRateDropped
        expr: rate(bookings_created_total[1h]) < rate(bookings_created_total[1h] offset 24h) * 0.7
        for: 30m
        labels:
          severity: warning
          team: business
          component: bookings
        annotations:
          summary: "Baisse significative du taux de réservations"
          description: "Le taux de réservations a chuté de plus de 30% par rapport à hier"

      - alert: PaymentFailureRateHigh
        expr: rate(payments_failed_total[10m]) / rate(payments_total[10m]) > 0.05
        for: 5m
        labels:
          severity: critical
          team: business
          component: payments
        annotations:
          summary: "Taux d'échec de paiement élevé"
          description: "{{ $value | humanizePercentage }} des paiements échouent"

      # Utilisateurs
      - alert: UserRegistrationDropped
        expr: rate(users_registered_total[1h]) < rate(users_registered_total[1h] offset 24h) * 0.5
        for: 1h
        labels:
          severity: warning
          team: business
          component: users
        annotations:
          summary: "Baisse des inscriptions utilisateur"
          description: "Les inscriptions ont chuté de plus de 50% par rapport à hier"

      # =====================================================
      # ALERTES SÉCURITÉ
      # =====================================================

      - alert: SecurityVulnerabilityDetected
        expr: security_vulnerabilities_critical > 0
        for: 0s
        labels:
          severity: critical
          team: security
          component: security
          excellence_criteria: security
        annotations:
          summary: "Vulnérabilité critique détectée"
          description: "{{ $value }} vulnérabilités critiques détectées - Excellence 10/10 compromise"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/security-incident"

      - alert: UnauthorizedAccessAttempts
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          team: security
          component: auth
        annotations:
          summary: "Tentatives d'accès non autorisé élevées"
          description: "{{ $value }} tentatives d'accès non autorisé par seconde"

      # =====================================================
      # ALERTES DISPONIBILITÉ
      # =====================================================

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          team: devops
          component: availability
          excellence_criteria: availability
        annotations:
          summary: "Service indisponible"
          description: "Le service {{ $labels.job }} est indisponible"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/service-down"

      - alert: HealthCheckFailing
        expr: probe_success == 0
        for: 2m
        labels:
          severity: critical
          team: devops
          component: availability
        annotations:
          summary: "Health check en échec"
          description: "Le health check pour {{ $labels.instance }} échoue"

      # =====================================================
      # ALERTES EXCELLENCE 10/10 GLOBALES
      # =====================================================

      - alert: ExcellenceScoreBelow10
        expr: |
          (
            (histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) <= 0.15) +
            (rate(http_requests_total[5m]) >= 2000) +
            (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) <= 0.01) +
            (up == 1) +
            (redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) >= 0.95)
          ) / 5 * 10 < 10
        for: 5m
        labels:
          severity: critical
          team: all
          component: excellence
          excellence_criteria: overall
        annotations:
          summary: "Score d'excellence inférieur à 10/10"
          description: "Le score d'excellence global est de {{ $value }}/10, inférieur à l'objectif"
          runbook_url: "https://docs.retreat-and-be.com/runbooks/excellence-recovery"
          dashboard_url: "https://grafana.retreat-and-be.com/d/excellence-dashboard"

  # =====================================================
  # RÈGLES D'ENREGISTREMENT POUR OPTIMISATION
  # =====================================================
  - name: retreat-and-be-recording-rules
    interval: 30s
    rules:
      # Métriques de performance agrégées
      - record: retreat_and_be:http_request_duration_seconds:p95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

      - record: retreat_and_be:http_request_duration_seconds:p99
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))

      - record: retreat_and_be:http_requests:rate5m
        expr: rate(http_requests_total[5m])

      - record: retreat_and_be:http_requests:error_rate5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

      # Score d'excellence en temps réel
      - record: retreat_and_be:excellence_score
        expr: |
          (
            (retreat_and_be:http_request_duration_seconds:p95 <= 0.15) +
            (retreat_and_be:http_requests:rate5m >= 2000) +
            (retreat_and_be:http_requests:error_rate5m <= 0.01) +
            (up{job="retreat-and-be-backend"} == 1) +
            (redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) >= 0.95)
          ) / 5 * 10

      # Métriques business agrégées
      - record: retreat_and_be:bookings:rate1h
        expr: rate(bookings_created_total[1h])

      - record: retreat_and_be:revenue:rate1h
        expr: rate(revenue_total[1h])

      - record: retreat_and_be:users:active_rate1h
        expr: rate(users_active_total[1h])
