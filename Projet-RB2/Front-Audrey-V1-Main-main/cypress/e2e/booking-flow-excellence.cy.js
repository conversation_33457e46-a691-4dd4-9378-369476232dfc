describe('Flux de Réservation Excellence 10/10', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.viewport(1280, 720)
  })

  it('Réservation complète avec paiement', () => {
    // Connexion utilisateur
    cy.get('[data-cy=login-button]').click()
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('password123')
    cy.get('[data-cy=login-submit]').click()
    
    // Recherche activité
    cy.get('[data-cy=search-input]').type('Yoga Excellence')
    cy.get('[data-cy=search-button]').click()
    
    // Sélection et réservation
    cy.get('[data-cy=activity-card]').first().click()
    cy.get('[data-cy=book-button]').click()
    
    // Paiement
    cy.get('[data-cy=payment-form]').should('be.visible')
    cy.get('[data-cy=card-number]').type('****************')
    cy.get('[data-cy=pay-button]').click()
    
    // Confirmation
    cy.contains('Réservation confirmée').should('be.visible')
  })
})
