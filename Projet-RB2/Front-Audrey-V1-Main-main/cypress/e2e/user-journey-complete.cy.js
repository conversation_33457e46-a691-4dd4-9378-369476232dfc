describe('Parcours Utilisateur Complet - Excellence 10/10', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.viewport(1280, 720)
  })

  it('Inscription → Connexion → Réservation → Paiement', () => {
    // 1. Inscription
    cy.get('[data-cy=signup-button]', { timeout: 10000 }).should('be.visible').click()
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('SecurePassword123!')
    cy.get('[data-cy=confirm-password-input]').type('SecurePassword123!')
    cy.get('[data-cy=signup-submit]').click()
    
    // Vérification email de confirmation
    cy.contains('Vérifiez votre email', { timeout: 5000 }).should('be.visible')
    
    // 2. Connexion (simulation email confirmé)
    cy.visit('/login')
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('SecurePassword123!')
    cy.get('[data-cy=login-submit]').click()
    
    // Vérification connexion réussie
    cy.url().should('include', '/dashboard')
    cy.get('[data-cy=user-menu]').should('be.visible')
    
    // 3. Recherche et réservation
    cy.get('[data-cy=search-input]').type('Yoga Excellence Paris')
    cy.get('[data-cy=search-button]').click()
    
    // Attendre les résultats
    cy.get('[data-cy=search-results]', { timeout: 10000 }).should('be.visible')
    
    // Sélection d'une activité
    cy.get('[data-cy=activity-card]').first().click()
    cy.get('[data-cy=book-button]').should('be.visible').click()
    
    // Sélection date et heure
    cy.get('[data-cy=date-picker]').click()
    cy.get('[data-cy=available-date]').first().click()
    cy.get('[data-cy=time-slot]').first().click()
    cy.get('[data-cy=confirm-booking]').click()
    
    // 4. Paiement
    cy.get('[data-cy=payment-method]').select('card')
    cy.get('[data-cy=card-number]').type('****************')
    cy.get('[data-cy=card-expiry]').type('12/25')
    cy.get('[data-cy=card-cvc]').type('123')
    cy.get('[data-cy=cardholder-name]').type('Test Excellence User')
    cy.get('[data-cy=pay-button]').click()
    
    // Vérification confirmation
    cy.contains('Réservation confirmée', { timeout: 15000 }).should('be.visible')
    cy.get('[data-cy=booking-reference]').should('be.visible')
    cy.get('[data-cy=booking-reference]').should('contain', 'RB-')
  })

  it('Test Responsive Mobile - Excellence', () => {
    // Test iPhone X
    cy.viewport('iphone-x')
    
    // Navigation mobile
    cy.get('[data-cy=mobile-menu-button]').should('be.visible').click()
    cy.get('[data-cy=mobile-menu]').should('be.visible')
    
    // Recherche mobile
    cy.get('[data-cy=mobile-search]').type('Méditation Excellence')
    cy.get('[data-cy=mobile-search-button]').click()
    
    // Vérification affichage mobile
    cy.get('[data-cy=activity-card]').should('be.visible')
    cy.get('[data-cy=activity-card]').should('have.css', 'width')
    
    // Test iPad
    cy.viewport('ipad-2')
    cy.reload()
    
    // Vérification layout tablet
    cy.get('[data-cy=main-navigation]').should('be.visible')
    cy.get('[data-cy=search-bar]').should('be.visible')
  })

  it('Test Accessibilité WCAG 2.1 AA - Excellence', () => {
    // Vérification des contrastes
    cy.get('body').should('have.css', 'color')
    
    // Navigation au clavier
    cy.get('body').tab()
    cy.focused().should('have.attr', 'data-cy')
    
    // Test navigation complète au clavier
    for (let i = 0; i < 10; i++) {
      cy.focused().tab()
      cy.focused().should('be.visible')
    }
    
    // Attributs ARIA
    cy.get('[role=button]').should('exist')
    cy.get('[aria-label]').should('exist')
    cy.get('[aria-describedby]').should('exist')
    
    // Images avec alt text
    cy.get('img').each(($img) => {
      cy.wrap($img).should('have.attr', 'alt')
    })
    
    // Liens avec texte descriptif
    cy.get('a').each(($link) => {
      cy.wrap($link).should('not.be.empty')
    })
    
    // Formulaires avec labels
    cy.get('input[type="text"], input[type="email"], input[type="password"]').each(($input) => {
      const id = $input.attr('id')
      if (id) {
        cy.get(`label[for="${id}"]`).should('exist')
      }
    })
  })

  it('Test Performance Core Web Vitals - Excellence', () => {
    // Mesure du LCP (Largest Contentful Paint)
    cy.window().then((win) => {
      const observer = new win.PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        expect(lastEntry.startTime).to.be.lessThan(2500) // LCP < 2.5s
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    })
    
    // Mesure du CLS (Cumulative Layout Shift)
    cy.window().then((win) => {
      let clsValue = 0
      const observer = new win.PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        }
        expect(clsValue).to.be.lessThan(0.1) // CLS < 0.1
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    })
    
    // Test de la vitesse de chargement
    cy.window().its('performance').then((performance) => {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
      expect(loadTime).to.be.lessThan(3000) // Chargement < 3s
    })
  })

  it('Test Cross-Browser Compatibility - Excellence', () => {
    // Test des fonctionnalités modernes
    cy.window().then((win) => {
      expect(win.fetch).to.exist
      expect(win.Promise).to.exist
      expect(win.localStorage).to.exist
      expect(win.sessionStorage).to.exist
    })
    
    // Test CSS Grid et Flexbox
    cy.get('[data-cy=main-layout]').should('have.css', 'display', 'grid')
    cy.get('[data-cy=flex-container]').should('have.css', 'display', 'flex')
    
    // Test des API modernes
    cy.window().then((win) => {
      if ('serviceWorker' in win.navigator) {
        expect(win.navigator.serviceWorker).to.exist
      }
    })
  })

  it('Test Sécurité Frontend - Excellence', () => {
    // Vérification CSP headers
    cy.request('/').then((response) => {
      expect(response.headers).to.have.property('content-security-policy')
    })
    
    // Test XSS protection
    const maliciousScript = '<script>alert("XSS")</script>'
    cy.get('[data-cy=search-input]').type(maliciousScript)
    cy.get('[data-cy=search-button]').click()
    
    // Vérifier que le script n'est pas exécuté
    cy.on('window:alert', () => {
      throw new Error('XSS vulnerability detected!')
    })
    
    // Test CSRF protection
    cy.window().then((win) => {
      const csrfToken = win.document.querySelector('meta[name="csrf-token"]')
      expect(csrfToken).to.exist
    })
  })

  it('Test Intégration API - Excellence', () => {
    // Test des appels API
    cy.intercept('GET', '/api/activities', { fixture: 'activities.json' }).as('getActivities')
    cy.intercept('POST', '/api/bookings', { fixture: 'booking-success.json' }).as('createBooking')
    
    // Navigation vers la recherche
    cy.get('[data-cy=search-input]').type('Test API')
    cy.get('[data-cy=search-button]').click()
    
    // Vérification de l'appel API
    cy.wait('@getActivities').then((interception) => {
      expect(interception.response.statusCode).to.equal(200)
    })
    
    // Test de création de réservation
    cy.get('[data-cy=activity-card]').first().click()
    cy.get('[data-cy=book-button]').click()
    
    // Remplir le formulaire de réservation
    cy.get('[data-cy=date-picker]').click()
    cy.get('[data-cy=available-date]').first().click()
    cy.get('[data-cy=confirm-booking]').click()
    
    // Vérification de l'appel API de création
    cy.wait('@createBooking').then((interception) => {
      expect(interception.response.statusCode).to.equal(201)
    })
  })

  it('Test Gestion d\'Erreurs - Excellence', () => {
    // Test erreur réseau
    cy.intercept('GET', '/api/activities', { forceNetworkError: true }).as('networkError')
    
    cy.get('[data-cy=search-input]').type('Test Error')
    cy.get('[data-cy=search-button]').click()
    
    // Vérification du message d'erreur
    cy.contains('Erreur de connexion', { timeout: 10000 }).should('be.visible')
    cy.get('[data-cy=retry-button]').should('be.visible')
    
    // Test erreur 500
    cy.intercept('GET', '/api/activities', { statusCode: 500 }).as('serverError')
    
    cy.get('[data-cy=retry-button]').click()
    cy.contains('Erreur serveur').should('be.visible')
    
    // Test erreur 404
    cy.visit('/page-inexistante')
    cy.contains('Page non trouvée').should('be.visible')
    cy.get('[data-cy=home-link]').should('be.visible')
  })
})
