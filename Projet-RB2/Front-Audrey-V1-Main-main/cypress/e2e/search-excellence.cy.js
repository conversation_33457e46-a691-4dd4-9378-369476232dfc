describe('Recherche Excellence 10/10', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('Recherche avancée avec filtres', () => {
    cy.get('[data-cy=search-input]').type('Méditation')
    cy.get('[data-cy=location-filter]').select('Paris')
    cy.get('[data-cy=price-filter]').select('50-100')
    cy.get('[data-cy=search-button]').click()
    
    cy.get('[data-cy=search-results]').should('be.visible')
    cy.get('[data-cy=activity-card]').should('have.length.greaterThan', 0)
  })

  it('Recherche avec suggestions', () => {
    cy.get('[data-cy=search-input]').type('Yog')
    cy.get('[data-cy=search-suggestions]').should('be.visible')
    cy.get('[data-cy=suggestion-item]').first().click()
    
    cy.get('[data-cy=search-results]').should('be.visible')
  })
})
