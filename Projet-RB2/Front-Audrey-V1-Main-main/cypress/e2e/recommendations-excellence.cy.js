describe('Recommandations IA Excellence 10/10', () => {
  beforeEach(() => {
    cy.visit('/login')
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('password123')
    cy.get('[data-cy=login-submit]').click()
  })

  it('Affichage recommandations personnalisées', () => {
    cy.visit('/dashboard')
    
    cy.get('[data-cy=recommendations-section]').should('be.visible')
    cy.get('[data-cy=recommendation-card]').should('have.length.greaterThan', 0)
    
    // Test interaction avec recommandation
    cy.get('[data-cy=recommendation-card]').first().click()
    cy.get('[data-cy=activity-details]').should('be.visible')
  })

  it('Feedback sur recommandations', () => {
    cy.visit('/dashboard')
    
    cy.get('[data-cy=recommendation-card]').first().within(() => {
      cy.get('[data-cy=like-button]').click()
    })
    
    cy.contains('Merci pour votre feedback').should('be.visible')
  })
})
