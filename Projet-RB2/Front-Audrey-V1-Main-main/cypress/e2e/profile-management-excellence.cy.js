describe('Gestion Profil Excellence 10/10', () => {
  beforeEach(() => {
    cy.visit('/login')
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('password123')
    cy.get('[data-cy=login-submit]').click()
  })

  it('Modification profil utilisateur', () => {
    cy.get('[data-cy=user-menu]').click()
    cy.get('[data-cy=profile-link]').click()
    
    cy.get('[data-cy=first-name-input]').clear().type('Jean')
    cy.get('[data-cy=last-name-input]').clear().type('Dupont')
    cy.get('[data-cy=save-profile]').click()
    
    cy.contains('Profil mis à jour').should('be.visible')
  })

  it('Historique des réservations', () => {
    cy.get('[data-cy=user-menu]').click()
    cy.get('[data-cy=bookings-link]').click()
    
    cy.get('[data-cy=booking-history]').should('be.visible')
    cy.get('[data-cy=booking-item]').should('have.length.greaterThan', 0)
  })
})
