{"openapi": "3.0.0", "info": {"title": "Retreat And Be API - Excellence 10/10", "description": "API complète pour la plateforme Retreat And Be - Référence mondiale du bien-être", "version": "4.0.0", "contact": {"name": "Équipe Technique Retreat And Be", "email": "<EMAIL>", "url": "https://retreat-and-be.com"}, "license": {"name": "Proprietary", "url": "https://retreat-and-be.com/license"}}, "servers": [{"url": "https://api.retreat-and-be.com", "description": "Production Server"}, {"url": "https://staging-api.retreat-and-be.com", "description": "Staging Server"}, {"url": "http://localhost:3000", "description": "Development Server"}], "security": [{"bearerAuth": []}], "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health Check", "description": "Vérifie l'état de santé de l'API", "responses": {"200": {"description": "Service opérationnel", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "Connexion utilisateur", "description": "Authentifie un utilisateur et retourne un token JWT", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Connexion réussie", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "401": {"description": "Identifiants invalides"}}}}, "/auth/register": {"post": {"tags": ["Authentication"], "summary": "Inscription utilisateur", "description": "Crée un nouveau compte utilisateur", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "Compte c<PERSON><PERSON> avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "400": {"description": "Données invalides"}}}}, "/activities": {"get": {"tags": ["Activities"], "summary": "Liste des activités", "description": "Récupère la liste des activités disponibles", "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}, "description": "Filtrer par catégorie"}, {"name": "location", "in": "query", "schema": {"type": "string"}, "description": "Filtrer par localisation"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "Nombre d'éléments par page"}, {"name": "offset", "in": "query", "schema": {"type": "integer", "default": 0}, "description": "Décalage pour la pagination"}], "responses": {"200": {"description": "Liste des activités", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivitiesResponse"}}}}}}}, "/activities/search": {"get": {"tags": ["Activities"], "summary": "Recherche d'activités", "description": "Recherche d'activités avec critères avancés", "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Terme de recherche"}], "responses": {"200": {"description": "Résultats de recherche", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponse"}}}}}}}, "/activities/{id}": {"get": {"tags": ["Activities"], "summary": "Détails d'une activité", "description": "Récupère les détails d'une activité spécifique", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID de l'activité"}], "responses": {"200": {"description": "Détails de l'activité", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Activity"}}}}, "404": {"description": "Activité non trouvée"}}}}, "/bookings": {"post": {"tags": ["Bookings"], "summary": "C<PERSON>er une réservation", "description": "Crée une nouvelle réservation", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBookingRequest"}}}}, "responses": {"201": {"description": "Réservation créée", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Booking"}}}}, "400": {"description": "Données invalides"}, "401": {"description": "Non authentifié"}}}}, "/recommendations": {"get": {"tags": ["Recommendations"], "summary": "Recommandations personnalisées", "description": "Récupère les recommandations personnalisées pour l'utilisateur", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Recommandations personnalisées", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationsResponse"}}}}, "401": {"description": "Non authentifié"}}}}, "/metrics": {"get": {"tags": ["Monitoring"], "summary": "Métriques Prometheus", "description": "Endpoint pour les métriques Prometheus", "responses": {"200": {"description": "Métriques au format Prometheus", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"HealthResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "timestamp": {"type": "string", "format": "date-time"}, "uptime": {"type": "number"}, "version": {"type": "string", "example": "4.0.0"}}}, "LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 8, "example": "SecurePassword123!"}}}, "RegisterRequest": {"type": "object", "required": ["email", "password", "firstName", "lastName"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}}, "AuthResponse": {"type": "object", "properties": {"access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "expires_in": {"type": "number"}, "user": {"$ref": "#/components/schemas/User"}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string", "format": "email"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Activity": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "category": {"type": "string"}, "location": {"type": "string"}, "price": {"type": "number"}, "duration": {"type": "number"}, "maxParticipants": {"type": "number"}, "averageRating": {"type": "number"}, "images": {"type": "array", "items": {"type": "string"}}}}, "ActivitiesResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "total": {"type": "number"}, "page": {"type": "number"}, "limit": {"type": "number"}}}, "SearchResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "total": {"type": "number"}, "query": {"type": "string"}, "suggestions": {"type": "array", "items": {"type": "string"}}}}, "CreateBookingRequest": {"type": "object", "required": ["activity_id", "booking_date", "participants"], "properties": {"activity_id": {"type": "string"}, "booking_date": {"type": "string", "format": "date-time"}, "time_slot": {"type": "string"}, "participants": {"type": "number", "minimum": 1}}}, "Booking": {"type": "object", "properties": {"id": {"type": "string"}, "activity_id": {"type": "string"}, "user_id": {"type": "string"}, "booking_date": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["pending", "confirmed", "cancelled", "completed"]}, "total_amount": {"type": "number"}, "created_at": {"type": "string", "format": "date-time"}}}, "RecommendationsResponse": {"type": "object", "properties": {"recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "algorithm": {"type": "string"}, "confidence": {"type": "number"}, "generated_at": {"type": "string", "format": "date-time"}}}}}, "tags": [{"name": "Health", "description": "Endpoints de santé et monitoring"}, {"name": "Authentication", "description": "Authentification et autorisation"}, {"name": "Activities", "description": "Gestion des activités et retraites"}, {"name": "Bookings", "description": "Gestion des réservations"}, {"name": "Recommendations", "description": "Système de recommandations IA"}, {"name": "Monitoring", "description": "Monitoring et métriques"}]}