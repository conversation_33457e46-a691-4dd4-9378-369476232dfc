-- OPTIMISATIONS BASE DE DONNÉES POUR EXCELLENCE 10/10
-- Script d'optimisation des performances pour Retreat And Be

-- =====================================================
-- INDEX POUR LES REQUÊTES FRÉQUENTES
-- =====================================================

-- Index pour les utilisateurs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login ON users(last_login_at);

-- Index pour les réservations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_activity_id ON bookings(activity_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_date ON bookings(booking_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);

-- Index pour les activités
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_category ON activities(category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_location ON activities(location);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_status ON activities(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_price ON activities(price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_rating ON activities(average_rating);

-- Index pour les paiements
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_booking_id ON payments(booking_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- =====================================================
-- INDEX COMPOSITES POUR LES RECHERCHES COMPLEXES
-- =====================================================

-- Recherche d'activités par catégorie et localisation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_category_location 
ON activities(category, location) WHERE status = 'active';

-- Recherche d'activités par prix et note
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_price_rating 
ON activities(price, average_rating) WHERE status = 'active';

-- Réservations par utilisateur et date
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_user_date 
ON bookings(user_id, booking_date) WHERE status IN ('confirmed', 'pending');

-- Activités populaires (par nombre de réservations)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_popularity 
ON activities(booking_count DESC, average_rating DESC) WHERE status = 'active';

-- =====================================================
-- INDEX POUR LES RECOMMANDATIONS
-- =====================================================

-- Préférences utilisateur
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_preferences_user_id 
ON user_preferences(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_preferences_category 
ON user_preferences(category, preference_score DESC);

-- Historique des réservations pour recommandations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_history_recommendations 
ON bookings(user_id, activity_id, created_at DESC) 
WHERE status = 'completed';

-- =====================================================
-- INDEX POUR LES RECHERCHES TEXTUELLES
-- =====================================================

-- Index GIN pour la recherche full-text sur les activités
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_search 
ON activities USING gin(to_tsvector('french', title || ' ' || description));

-- Index pour la recherche par tags
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_tags 
ON activities USING gin(tags);

-- =====================================================
-- INDEX POUR LES MÉTRIQUES ET ANALYTICS
-- =====================================================

-- Analytics des réservations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_bookings_date 
ON bookings(DATE(created_at), status);

-- Analytics des revenus
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_payments_date 
ON payments(DATE(created_at), amount) WHERE status = 'completed';

-- Analytics des utilisateurs actifs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_users_activity 
ON users(DATE(last_login_at)) WHERE status = 'active';

-- =====================================================
-- OPTIMISATIONS DES REQUÊTES EXISTANTES
-- =====================================================

-- Vue matérialisée pour les activités populaires
CREATE MATERIALIZED VIEW IF NOT EXISTS popular_activities AS
SELECT 
    a.id,
    a.title,
    a.category,
    a.location,
    a.price,
    a.average_rating,
    COUNT(b.id) as booking_count,
    AVG(r.rating) as recent_rating
FROM activities a
LEFT JOIN bookings b ON a.id = b.activity_id AND b.status = 'completed'
LEFT JOIN reviews r ON a.id = r.activity_id AND r.created_at > NOW() - INTERVAL '30 days'
WHERE a.status = 'active'
GROUP BY a.id, a.title, a.category, a.location, a.price, a.average_rating
ORDER BY booking_count DESC, recent_rating DESC;

-- Index sur la vue matérialisée
CREATE INDEX IF NOT EXISTS idx_popular_activities_category 
ON popular_activities(category, booking_count DESC);

-- Vue matérialisée pour les statistiques utilisateur
CREATE MATERIALIZED VIEW IF NOT EXISTS user_statistics AS
SELECT 
    u.id,
    u.email,
    COUNT(b.id) as total_bookings,
    SUM(p.amount) as total_spent,
    AVG(r.rating) as average_rating_given,
    MAX(b.created_at) as last_booking_date
FROM users u
LEFT JOIN bookings b ON u.id = b.user_id AND b.status = 'completed'
LEFT JOIN payments p ON b.id = p.booking_id AND p.status = 'completed'
LEFT JOIN reviews r ON u.id = r.user_id
WHERE u.status = 'active'
GROUP BY u.id, u.email;

-- =====================================================
-- PROCÉDURES DE MAINTENANCE
-- =====================================================

-- Fonction pour rafraîchir les vues matérialisées
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY popular_activities;
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_statistics;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STATISTIQUES POUR L'OPTIMISEUR
-- =====================================================

-- Mise à jour des statistiques pour l'optimiseur de requêtes
ANALYZE users;
ANALYZE bookings;
ANALYZE activities;
ANALYZE payments;
ANALYZE reviews;
ANALYZE user_preferences;

-- =====================================================
-- CONFIGURATION DES PARAMÈTRES DE PERFORMANCE
-- =====================================================

-- Augmenter la mémoire de travail pour les requêtes complexes
-- (À appliquer au niveau de la session ou de la base)
-- SET work_mem = '256MB';
-- SET shared_buffers = '1GB';
-- SET effective_cache_size = '4GB';

-- =====================================================
-- REQUÊTES D'EXEMPLE OPTIMISÉES
-- =====================================================

-- Recherche d'activités optimisée
/*
EXPLAIN (ANALYZE, BUFFERS) 
SELECT a.id, a.title, a.category, a.location, a.price, a.average_rating
FROM activities a
WHERE a.category = 'yoga' 
  AND a.location = 'paris'
  AND a.status = 'active'
  AND a.price BETWEEN 50 AND 150
ORDER BY a.average_rating DESC, a.booking_count DESC
LIMIT 20;
*/

-- Recommandations utilisateur optimisées
/*
EXPLAIN (ANALYZE, BUFFERS)
SELECT DISTINCT a.id, a.title, a.category, a.average_rating
FROM activities a
JOIN user_preferences up ON a.category = up.category
WHERE up.user_id = $1
  AND a.status = 'active'
  AND a.id NOT IN (
    SELECT activity_id 
    FROM bookings 
    WHERE user_id = $1 AND status IN ('confirmed', 'completed')
  )
ORDER BY up.preference_score DESC, a.average_rating DESC
LIMIT 10;
*/

-- =====================================================
-- MONITORING DES PERFORMANCES
-- =====================================================

-- Vue pour surveiller les requêtes lentes
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 100  -- Requêtes > 100ms en moyenne
ORDER BY mean_time DESC;

-- Vue pour surveiller l'utilisation des index
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Unused'
        WHEN idx_scan < 100 THEN 'Low usage'
        ELSE 'Good usage'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- =====================================================
-- NETTOYAGE ET MAINTENANCE AUTOMATIQUE
-- =====================================================

-- Fonction de nettoyage des données obsolètes
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Supprimer les sessions expirées (> 30 jours)
    DELETE FROM user_sessions 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Supprimer les logs anciens (> 90 jours)
    DELETE FROM activity_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Archiver les réservations anciennes (> 1 an)
    INSERT INTO bookings_archive 
    SELECT * FROM bookings 
    WHERE created_at < NOW() - INTERVAL '1 year' 
      AND status = 'completed';
    
    DELETE FROM bookings 
    WHERE created_at < NOW() - INTERVAL '1 year' 
      AND status = 'completed';
      
    -- Vacuum et reindex
    VACUUM ANALYZE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTAIRES ET DOCUMENTATION
-- =====================================================

COMMENT ON INDEX idx_activities_category_location IS 'Index composite pour recherche par catégorie et localisation';
COMMENT ON INDEX idx_bookings_user_date IS 'Index pour requêtes de réservations par utilisateur et date';
COMMENT ON MATERIALIZED VIEW popular_activities IS 'Vue matérialisée des activités populaires, rafraîchie quotidiennement';
COMMENT ON FUNCTION refresh_materialized_views() IS 'Rafraîchit toutes les vues matérialisées pour les performances';
COMMENT ON FUNCTION cleanup_old_data() IS 'Nettoyage automatique des données obsolètes';

-- =====================================================
-- VALIDATION DES OPTIMISATIONS
-- =====================================================

-- Vérifier que tous les index ont été créés
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
