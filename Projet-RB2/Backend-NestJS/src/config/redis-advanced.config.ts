import { CacheModuleOptions } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';

export class RedisAdvancedConfig {
  static getConfig(configService: ConfigService): CacheModuleOptions {
    return {
      store: redisStore,
      host: configService.get('REDIS_HOST', 'localhost'),
      port: configService.get('REDIS_PORT', 6379),
      password: configService.get('REDIS_PASSWORD'),
      db: configService.get('REDIS_DB', 0),
      
      // Configuration avancée pour excellence 10/10
      ttl: 3600, // 1 heure par défaut
      max: 10000, // Maximum 10000 clés en cache
      
      // Pool de connexions optimisé
      family: 4,
      keepAlive: true,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxLoadingTimeout: 5000,
      
      // Stratégie de retry optimisée
      retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
          console.error('Redis server connection refused');
          return new Error('Redis server connection refused');
        }
        
        if (options.total_retry_time > 1000 * 60 * 60) {
          console.error('Retry time exhausted');
          return new Error('Retry time exhausted');
        }
        
        if (options.attempt > 10) {
          console.error('Max retry attempts reached');
          return undefined;
        }
        
        // Backoff exponentiel avec jitter
        const delay = Math.min(options.attempt * 100, 3000);
        const jitter = Math.random() * 100;
        return delay + jitter;
      },
      
      // Configuration de performance
      connect_timeout: 60000,
      command_timeout: 5000,
      socket_keepalive: true,
      socket_initial_delay: 0,
      
      // Compression pour les grandes valeurs
      compression: 'gzip',
      
      // Sérialisation optimisée
      serialize: (value) => {
        try {
          return JSON.stringify(value);
        } catch (error) {
          console.error('Redis serialization error:', error);
          return null;
        }
      },
      
      deserialize: (value) => {
        try {
          return JSON.parse(value);
        } catch (error) {
          console.error('Redis deserialization error:', error);
          return null;
        }
      },
    };
  }
}

// Stratégies de cache spécialisées
export class CacheStrategies {
  // Cache pour les données utilisateur (TTL court)
  static readonly USER_DATA = {
    ttl: 300, // 5 minutes
    prefix: 'user:',
  };
  
  // Cache pour les activités (TTL moyen)
  static readonly ACTIVITIES = {
    ttl: 1800, // 30 minutes
    prefix: 'activities:',
  };
  
  // Cache pour les recommandations (TTL long)
  static readonly RECOMMENDATIONS = {
    ttl: 7200, // 2 heures
    prefix: 'recommendations:',
  };
  
  // Cache pour les données statiques (TTL très long)
  static readonly STATIC_DATA = {
    ttl: 86400, // 24 heures
    prefix: 'static:',
  };
  
  // Cache pour les sessions (TTL personnalisé)
  static readonly SESSIONS = {
    ttl: 3600, // 1 heure
    prefix: 'session:',
  };
  
  // Cache pour les métriques (TTL court)
  static readonly METRICS = {
    ttl: 60, // 1 minute
    prefix: 'metrics:',
  };
}

// Service de cache avancé avec patterns optimisés
export class AdvancedCacheService {
  constructor(private readonly cacheManager: any) {}
  
  // Pattern: Cache-Aside avec fallback
  async getWithFallback<T>(
    key: string,
    fallbackFn: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    try {
      // Tentative de récupération depuis le cache
      const cached = await this.cacheManager.get(key);
      if (cached !== null && cached !== undefined) {
        return cached;
      }
      
      // Fallback vers la source de données
      const data = await fallbackFn();
      
      // Mise en cache asynchrone (fire-and-forget)
      this.cacheManager.set(key, data, ttl).catch(error => {
        console.error('Cache set error:', error);
      });
      
      return data;
    } catch (error) {
      console.error('Cache get error:', error);
      // En cas d'erreur cache, utiliser directement le fallback
      return fallbackFn();
    }
  }
  
  // Pattern: Write-Through Cache
  async setWithWriteThrough<T>(
    key: string,
    value: T,
    persistFn: (value: T) => Promise<void>,
    ttl: number = 3600
  ): Promise<void> {
    try {
      // Écriture en base de données
      await persistFn(value);
      
      // Mise en cache
      await this.cacheManager.set(key, value, ttl);
    } catch (error) {
      console.error('Write-through cache error:', error);
      throw error;
    }
  }
  
  // Pattern: Cache invalidation avec tags
  async invalidateByPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.cacheManager.store.keys(pattern);
      if (keys.length > 0) {
        await this.cacheManager.store.del(...keys);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }
  
  // Pattern: Bulk operations optimisées
  async mget(keys: string[]): Promise<any[]> {
    try {
      return await this.cacheManager.store.mget(...keys);
    } catch (error) {
      console.error('Cache mget error:', error);
      return new Array(keys.length).fill(null);
    }
  }
  
  async mset(keyValuePairs: Array<{key: string, value: any, ttl?: number}>): Promise<void> {
    try {
      const pipeline = this.cacheManager.store.pipeline();
      
      keyValuePairs.forEach(({key, value, ttl = 3600}) => {
        pipeline.setex(key, ttl, JSON.stringify(value));
      });
      
      await pipeline.exec();
    } catch (error) {
      console.error('Cache mset error:', error);
    }
  }
  
  // Pattern: Cache warming
  async warmCache(warmingFunctions: Array<() => Promise<void>>): Promise<void> {
    try {
      await Promise.allSettled(warmingFunctions.map(fn => fn()));
    } catch (error) {
      console.error('Cache warming error:', error);
    }
  }
  
  // Métriques de performance du cache
  async getCacheStats(): Promise<any> {
    try {
      const info = await this.cacheManager.store.info('stats');
      return {
        hits: info.keyspace_hits || 0,
        misses: info.keyspace_misses || 0,
        hitRate: info.keyspace_hits / (info.keyspace_hits + info.keyspace_misses) || 0,
        memory: info.used_memory_human || '0B',
        connections: info.connected_clients || 0,
        uptime: info.uptime_in_seconds || 0,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return null;
    }
  }
}

// Configuration de monitoring Redis
export const RedisMonitoringConfig = {
  // Seuils d'alerte
  thresholds: {
    hitRate: 0.95, // Taux de hit minimum 95%
    memory: 0.8,   // Utilisation mémoire maximum 80%
    connections: 100, // Nombre maximum de connexions
    responseTime: 10, // Temps de réponse maximum 10ms
  },
  
  // Métriques à surveiller
  metrics: [
    'keyspace_hits',
    'keyspace_misses',
    'used_memory',
    'connected_clients',
    'total_commands_processed',
    'instantaneous_ops_per_sec',
  ],
  
  // Alertes automatiques
  alerts: {
    lowHitRate: true,
    highMemoryUsage: true,
    highConnectionCount: true,
    slowResponse: true,
  },
};

// Utilitaires pour les clés de cache
export class CacheKeyUtils {
  static buildKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }
  
  static buildUserKey(userId: string, suffix?: string): string {
    return suffix 
      ? this.buildKey(CacheStrategies.USER_DATA.prefix, userId, suffix)
      : this.buildKey(CacheStrategies.USER_DATA.prefix, userId);
  }
  
  static buildActivityKey(activityId: string, suffix?: string): string {
    return suffix
      ? this.buildKey(CacheStrategies.ACTIVITIES.prefix, activityId, suffix)
      : this.buildKey(CacheStrategies.ACTIVITIES.prefix, activityId);
  }
  
  static buildRecommendationKey(userId: string, type: string): string {
    return this.buildKey(CacheStrategies.RECOMMENDATIONS.prefix, userId, type);
  }
  
  static buildSessionKey(sessionId: string): string {
    return this.buildKey(CacheStrategies.SESSIONS.prefix, sessionId);
  }
  
  static buildMetricKey(metric: string, timestamp?: number): string {
    const ts = timestamp || Math.floor(Date.now() / 60000); // Minute precision
    return this.buildKey(CacheStrategies.METRICS.prefix, metric, ts);
  }
}
