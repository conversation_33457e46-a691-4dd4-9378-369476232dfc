/**
 * Service API pour la gestion des métriques
 * Communication avec le système de monitoring et métriques
 */

import { apiClient } from './apiClient';

export interface MetricData {
  timestamp: Date;
  value: number;
  label?: string;
}

export interface ChartData {
  name: string;
  data: MetricData[];
  color: string;
  unit: string;
}

export interface MetricDefinition {
  id: string;
  name: string;
  description: string;
  unit: string;
  type: 'gauge' | 'counter' | 'histogram';
  category: string;
  color: string;
}

class MetricsService {
  private readonly baseUrl = '/api/metrics';

  /**
   * Obtenir l'historique des métriques
   */
  async getMetricsHistory(
    timeRange: string,
    metrics: string[]
  ): Promise<{ data: ChartData[] }> {
    try {
      const params = new URLSearchParams();
      params.append('range', timeRange);
      metrics.forEach(metric => params.append('metrics', metric));

      const response = await apiClient.get(`${this.baseUrl}/history?${params}`);
      
      return {
        data: response.data.map((chart: any) => ({
          ...chart,
          data: chart.data.map((point: any) => ({
            ...point,
            timestamp: new Date(point.timestamp)
          }))
        }))
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des métriques:', error);
      
      // Données de simulation
      const simulatedData: ChartData[] = [];
      const now = new Date();
      const timeRangeMs = this.getTimeRangeMs(timeRange);
      const points = Math.min(100, Math.max(10, timeRangeMs / (5 * 60 * 1000))); // Un point toutes les 5 minutes max
      
      const metricDefinitions = this.getMetricDefinitions();
      
      metrics.forEach(metricId => {
        const definition = metricDefinitions.find(m => m.id === metricId);
        if (!definition) return;
        
        const data: MetricData[] = [];
        
        for (let i = 0; i < points; i++) {
          const timestamp = new Date(now.getTime() - (timeRangeMs * (points - i - 1) / points));
          let value: number;
          
          // Générer des données réalistes selon le type de métrique
          switch (metricId) {
            case 'response_time':
              value = 200 + Math.sin(i / 10) * 100 + Math.random() * 50;
              break;
            case 'cpu_usage':
              value = 40 + Math.sin(i / 15) * 20 + Math.random() * 10;
              break;
            case 'memory_usage':
              value = 60 + Math.sin(i / 20) * 15 + Math.random() * 8;
              break;
            case 'disk_usage':
              value = 30 + (i / points) * 10 + Math.random() * 5;
              break;
            case 'network_io':
              value = 10 + Math.sin(i / 8) * 5 + Math.random() * 3;
              break;
            case 'requests_per_second':
              value = 50 + Math.sin(i / 12) * 30 + Math.random() * 15;
              break;
            case 'error_rate':
              value = 1 + Math.random() * 3;
              break;
            case 'active_connections':
              value = 100 + Math.sin(i / 18) * 50 + Math.random() * 20;
              break;
            default:
              value = Math.random() * 100;
          }
          
          data.push({
            timestamp,
            value: Math.max(0, value)
          });
        }
        
        simulatedData.push({
          name: definition.name,
          data,
          color: definition.color,
          unit: definition.unit
        });
      });
      
      return { data: simulatedData };
    }
  }

  /**
   * Obtenir les métriques en temps réel
   */
  async getRealTimeMetrics(metrics: string[]): Promise<{ data: Record<string, number> }> {
    try {
      const params = new URLSearchParams();
      metrics.forEach(metric => params.append('metrics', metric));

      const response = await apiClient.get(`${this.baseUrl}/realtime?${params}`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques temps réel:', error);
      
      // Données de simulation
      const data: Record<string, number> = {};
      metrics.forEach(metric => {
        switch (metric) {
          case 'response_time':
            data[metric] = 250 + Math.random() * 100;
            break;
          case 'cpu_usage':
            data[metric] = 45 + Math.random() * 20;
            break;
          case 'memory_usage':
            data[metric] = 65 + Math.random() * 15;
            break;
          case 'disk_usage':
            data[metric] = 35 + Math.random() * 10;
            break;
          case 'network_io':
            data[metric] = 12 + Math.random() * 8;
            break;
          case 'requests_per_second':
            data[metric] = 75 + Math.random() * 25;
            break;
          case 'error_rate':
            data[metric] = 1.5 + Math.random() * 2;
            break;
          case 'active_connections':
            data[metric] = 120 + Math.random() * 40;
            break;
          default:
            data[metric] = Math.random() * 100;
        }
      });
      
      return { data };
    }
  }

  /**
   * Obtenir les définitions des métriques disponibles
   */
  getMetricDefinitions(): MetricDefinition[] {
    return [
      {
        id: 'response_time',
        name: 'Temps de réponse',
        description: 'Temps de réponse moyen des requêtes HTTP',
        unit: 'ms',
        type: 'gauge',
        category: 'Performance',
        color: '#3B82F6'
      },
      {
        id: 'cpu_usage',
        name: 'Utilisation CPU',
        description: 'Pourcentage d\'utilisation du processeur',
        unit: '%',
        type: 'gauge',
        category: 'Système',
        color: '#EF4444'
      },
      {
        id: 'memory_usage',
        name: 'Utilisation mémoire',
        description: 'Pourcentage d\'utilisation de la mémoire',
        unit: '%',
        type: 'gauge',
        category: 'Système',
        color: '#10B981'
      },
      {
        id: 'disk_usage',
        name: 'Utilisation disque',
        description: 'Pourcentage d\'utilisation du disque',
        unit: '%',
        type: 'gauge',
        category: 'Système',
        color: '#F59E0B'
      },
      {
        id: 'network_io',
        name: 'I/O réseau',
        description: 'Débit réseau entrant et sortant',
        unit: 'MB/s',
        type: 'gauge',
        category: 'Réseau',
        color: '#8B5CF6'
      },
      {
        id: 'requests_per_second',
        name: 'Requêtes par seconde',
        description: 'Nombre de requêtes HTTP par seconde',
        unit: 'req/s',
        type: 'gauge',
        category: 'Performance',
        color: '#06B6D4'
      },
      {
        id: 'error_rate',
        name: 'Taux d\'erreur',
        description: 'Pourcentage de requêtes en erreur',
        unit: '%',
        type: 'gauge',
        category: 'Erreurs',
        color: '#DC2626'
      },
      {
        id: 'active_connections',
        name: 'Connexions actives',
        description: 'Nombre de connexions actives',
        unit: 'conn',
        type: 'gauge',
        category: 'Réseau',
        color: '#059669'
      }
    ];
  }

  /**
   * Obtenir les métriques d'un agent spécifique
   */
  async getAgentMetrics(agentId: string, timeRange: string = '1h'): Promise<{ data: ChartData[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/agents/${agentId}?range=${timeRange}`);
      
      return {
        data: response.data.map((chart: any) => ({
          ...chart,
          data: chart.data.map((point: any) => ({
            ...point,
            timestamp: new Date(point.timestamp)
          }))
        }))
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération des métriques de l'agent ${agentId}:`, error);
      return { data: [] };
    }
  }

  /**
   * Obtenir les métriques agrégées
   */
  async getAggregatedMetrics(
    metrics: string[],
    timeRange: string,
    aggregation: 'avg' | 'sum' | 'min' | 'max' = 'avg'
  ): Promise<{ data: Record<string, number> }> {
    try {
      const params = new URLSearchParams();
      params.append('range', timeRange);
      params.append('aggregation', aggregation);
      metrics.forEach(metric => params.append('metrics', metric));

      const response = await apiClient.get(`${this.baseUrl}/aggregated?${params}`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques agrégées:', error);
      return { data: {} };
    }
  }

  /**
   * Exporter les métriques
   */
  async exportMetrics(
    metrics: string[],
    timeRange: string,
    format: 'csv' | 'json' | 'xlsx' = 'csv'
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      params.append('range', timeRange);
      params.append('format', format);
      metrics.forEach(metric => params.append('metrics', metric));

      const response = await apiClient.get(`${this.baseUrl}/export?${params}`, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'export des métriques:', error);
      throw error;
    }
  }

  /**
   * Créer une métrique personnalisée
   */
  async createCustomMetric(metric: {
    name: string;
    description: string;
    unit: string;
    query: string;
    category: string;
  }): Promise<{ data: MetricDefinition }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/custom`, metric);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la création de la métrique personnalisée:', error);
      throw error;
    }
  }

  /**
   * Convertir une plage de temps en millisecondes
   */
  private getTimeRangeMs(timeRange: string): number {
    const unit = timeRange.slice(-1);
    const value = parseInt(timeRange.slice(0, -1));
    
    switch (unit) {
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      case 'm': return value * 60 * 1000;
      case 's': return value * 1000;
      default: return 60 * 60 * 1000; // 1 heure par défaut
    }
  }
}

export const metricsService = new MetricsService();
