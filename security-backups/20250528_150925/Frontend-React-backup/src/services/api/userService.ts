import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

// Types pour les utilisateurs
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  profilePicture?: string;
  bio?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserProfile {
  id: string;
  userId: string;
  bio?: string;
  location?: string;
  website?: string;
  interests?: string[];
  profilePicture?: string;
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  website?: string;
  interests?: string[];
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
}

/**
 * Service utilisateur pour gérer les opérations liées aux utilisateurs
 */
export const userService = {
  /**
   * Obtenir le profil de l'utilisateur connecté
   * @returns Profil de l'utilisateur
   */
  async getProfile(): Promise<User> {
    return apiClient.get<User>(API_ENDPOINTS.USER.PROFILE);
  },

  /**
   * Obtenir le profil détaillé de l'utilisateur connecté
   * @returns Profil détaillé de l'utilisateur
   */
  async getDetailedProfile(): Promise<UserProfile> {
    return apiClient.get<UserProfile>(API_ENDPOINTS.USER.DETAILED_PROFILE);
  },

  /**
   * Mettre à jour le profil de l'utilisateur
   * @param profileData Données du profil à mettre à jour
   * @returns Profil mis à jour
   */
  async updateProfile(profileData: UpdateProfileRequest): Promise<User> {
    return apiClient.put<User>(API_ENDPOINTS.USER.UPDATE_PROFILE, profileData);
  },

  /**
   * Télécharger une image de profil
   * @param file Fichier image
   * @returns URL de l'image téléchargée
   */
  async uploadProfilePicture(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post<{ url: string }>('/users/profile/picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Obtenir les informations d'un utilisateur par son ID
   * @param userId ID de l'utilisateur
   * @returns Informations de l'utilisateur
   */
  async getUserById(userId: string): Promise<User> {
    return apiClient.get<User>(`/users/${userId}`);
  },

  /**
   * Obtenir le profil détaillé d'un utilisateur par son ID
   * @param userId ID de l'utilisateur
   * @returns Profil détaillé de l'utilisateur
   */
  async getUserProfileById(userId: string): Promise<UserProfile> {
    return apiClient.get<UserProfile>(`/users/${userId}/profile`);
  },
};
