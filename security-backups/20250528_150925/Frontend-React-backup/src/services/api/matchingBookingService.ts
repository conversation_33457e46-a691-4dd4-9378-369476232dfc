import { apiClient } from './apiClient';
import { MatchingResult } from './matchingService';

interface CreateBookingParams {
  partnerId: string;
  retreatId: string;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
  services?: string[];
  participants?: number;
  specialRequirements?: string;
}

interface BookingCheckResult {
  exists: boolean;
  booking?: {
    id: string;
    status: string;
    createdAt: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    currency: string;
  };
}

interface ConversionStats {
  period: string;
  views: number;
  contacts: number;
  conversions: number;
  viewToContactRate: number;
  contactToBookingRate: number;
  viewToBookingRate: number;
}

// Define a generic booking response data type for methods returning 'any'
interface BookingResponseData {
  success: boolean;
  message?: string;
  data?: Record<string, any>; // Or a more specific type if known
  id?: string; // Example if an ID is returned
}

class MatchingBookingService {
  /**
   * Crée une réservation à partir d'un matching
   * @param params Paramètres de la réservation
   * @returns Résultat de la création
   */
  async createBookingFromMatching(params: CreateBookingParams): Promise<BookingResponseData> {
    try {
      const response = await apiClient.post<BookingResponseData>('/matching/booking/create', params);
      return response;
    } catch (error) {
      console.error('Error creating booking from matching:', error);
      throw error;
    }
  }

  /**
   * Vérifie si une réservation existe déjà pour un matching
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Résultat de la vérification
   */
  async checkExistingBooking(partnerId: string, retreatId: string): Promise<BookingCheckResult> {
    try {
      const response = await apiClient.get<BookingCheckResult>(`/matching/booking/check/${partnerId}/${retreatId}`);
      return response;
    } catch (error) {
      console.error('Error checking existing booking:', error);
      return { exists: false };
    }
  }

  /**
   * Obtient les statistiques de conversion des matchings en réservations
   * @param period Période (day, week, month, year)
   * @returns Statistiques de conversion
   */
  async getConversionStats(period: string = 'month'): Promise<ConversionStats> {
    try {
      const response = await apiClient.get<ConversionStats>(`/matching/booking/conversion-stats?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error getting conversion stats:', error);
      throw error;
    }
  }

  /**
   * Obtient les statistiques de conversion par catégorie de partenaire
   * @param period Période (day, week, month, year)
   * @returns Statistiques de conversion par catégorie
   */
  async getConversionStatsByCategory(period: string = 'month'): Promise<Record<string, any>[]> {
    try {
      const response = await apiClient.get<Record<string, any>[]>(`/matching/booking/conversion-stats/category?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error getting conversion stats by category:', error);
      throw error;
    }
  }

  /**
   * Obtient les informations sur une réservation créée à partir d'un matching
   * @param bookingId ID de la réservation
   * @returns Informations sur la réservation avec les détails de matching
   */
  async getBookingFromMatching(bookingId: string): Promise<BookingResponseData> {
    try {
      const response = await apiClient.get<BookingResponseData>(`/matching/booking/booking-from-matching/${bookingId}`);
      return response;
    } catch (error) {
      console.error('Error getting booking from matching:', error);
      throw error;
    }
  }
}

export const matchingBookingService = new MatchingBookingService();
