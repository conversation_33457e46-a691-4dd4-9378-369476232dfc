import { apiClient } from './apiClient';
import { AIService } from '../../../Agent IA/src/services/ai/AIService';

export interface ModerationRequest {
  content: string;
  contentType: 'TEXT' | 'IMAGE' | 'VIDEO' | 'COMMENT' | 'POST';
  context?: string;
}

export interface ModerationResult {
  isInappropriate: boolean;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | null;
  matchedRules: {
    id: string;
    name: string;
    pattern?: string;
    category?: string;
    threshold?: number;
    severity: string;
  }[];
  confidence: number;
  categories?: Record<string, number>;
  explanation?: string;
}

export interface ModerationRuleMatch {
  ruleId: string;
  ruleName: string;
  severity: string;
  matchedContent: string;
  confidence: number;
}

export interface TextModerationPrompt {
  content: string;
  context?: string;
  rules: {
    id: string;
    name: string;
    pattern: string;
    severity: string;
  }[];
}

export interface ImageModerationPrompt {
  imageUrl: string;
  base64Image?: string;
  context?: string;
  rules: {
    id: string;
    name: string;
    category: string;
    threshold: number;
    severity: string;
  }[];
}

class AIModerationService {
  private aiService: AIService | null = null;
  private initialized = false;

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // Utiliser le singleton AIService du microservice Agent IA
      this.aiService = AIService.getInstance();
      
      // Définir OpenAI comme fournisseur par défaut pour la modération
      await this.aiService.setProvider('openai');
      
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize AI Moderation Service:', error);
      throw new Error('AI Moderation Service initialization failed');
    }
  }

  /**
   * Modère du contenu textuel en utilisant l'IA
   */
  public async moderateText(content: string, rules: any[]): Promise<ModerationResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.aiService) {
      throw new Error('AI Service not available');
    }

    const prompt = this.buildTextModerationPrompt(content, rules);
    
    try {
      const response = await this.aiService.generateResponse(prompt);
      return this.parseAIResponse(response.text);
    } catch (error) {
      console.error('Error during text moderation:', error);
      throw new Error('Text moderation failed');
    }
  }

  /**
   * Modère une image en utilisant l'IA
   */
  public async moderateImage(imageUrl: string, base64Image: string, rules: any[]): Promise<ModerationResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.aiService) {
      throw new Error('AI Service not available');
    }

    const prompt = this.buildImageModerationPrompt(imageUrl, base64Image, rules);
    
    try {
      const response = await this.aiService.generateResponse(prompt);
      return this.parseAIResponse(response.text);
    } catch (error) {
      console.error('Error during image moderation:', error);
      throw new Error('Image moderation failed');
    }
  }

  /**
   * Construit un prompt pour la modération de texte
   */
  private buildTextModerationPrompt(content: string, rules: any[]): string {
    return `
You are a content moderation AI. Your task is to analyze the following text content and determine if it violates any of the provided moderation rules.

CONTENT TO MODERATE:
"""
${content}
"""

MODERATION RULES:
${rules.map(rule => `- Rule ID: ${rule.id}, Name: ${rule.name}, Pattern: ${rule.pattern}, Severity: ${rule.severity}`).join('\n')}

INSTRUCTIONS:
1. Analyze the content against each rule
2. For each rule, determine if the content violates it
3. Calculate a confidence score (0-1) for each match
4. Determine overall if the content is inappropriate
5. Assign an overall severity (LOW, MEDIUM, HIGH) if inappropriate
6. Provide a brief explanation of your decision

Return your analysis in the following JSON format:
{
  "isInappropriate": boolean,
  "severity": "LOW" | "MEDIUM" | "HIGH" | null,
  "matchedRules": [
    {
      "id": "rule-id",
      "name": "rule-name",
      "severity": "rule-severity",
      "confidence": number
    }
  ],
  "confidence": number,
  "explanation": "string"
}
`;
  }

  /**
   * Construit un prompt pour la modération d'image
   */
  private buildImageModerationPrompt(imageUrl: string, base64Image: string, rules: any[]): string {
    return `
You are a content moderation AI. Your task is to analyze the following image and determine if it violates any of the provided moderation rules.

IMAGE URL: ${imageUrl}
${base64Image ? `BASE64 IMAGE: ${base64Image.substring(0, 100)}...` : ''}

MODERATION RULES:
${rules.map(rule => `- Rule ID: ${rule.id}, Name: ${rule.name}, Category: ${rule.category}, Threshold: ${rule.threshold}, Severity: ${rule.severity}`).join('\n')}

INSTRUCTIONS:
1. Analyze the image against each rule category
2. For each category, determine if the image exceeds the threshold
3. Calculate a confidence score (0-1) for each match
4. Determine overall if the image is inappropriate
5. Assign an overall severity (LOW, MEDIUM, HIGH) if inappropriate
6. Provide a brief explanation of your decision

Return your analysis in the following JSON format:
{
  "isInappropriate": boolean,
  "severity": "LOW" | "MEDIUM" | "HIGH" | null,
  "matchedRules": [
    {
      "id": "rule-id",
      "name": "rule-name",
      "category": "category",
      "threshold": number,
      "severity": "rule-severity",
      "confidence": number
    }
  ],
  "confidence": number,
  "categories": {
    "category1": number,
    "category2": number
  },
  "explanation": "string"
}
`;
  }

  /**
   * Parse la réponse de l'IA en un objet ModerationResult
   */
  private parseAIResponse(responseText: string): ModerationResult {
    try {
      // Extraire le JSON de la réponse
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }
      
      const jsonResponse = JSON.parse(jsonMatch[0]);
      
      return {
        isInappropriate: jsonResponse.isInappropriate,
        severity: jsonResponse.severity,
        matchedRules: jsonResponse.matchedRules || [],
        confidence: jsonResponse.confidence,
        categories: jsonResponse.categories,
        explanation: jsonResponse.explanation
      };
    } catch (error) {
      console.error('Error parsing AI response:', error);
      // Retourner un résultat par défaut en cas d'erreur
      return {
        isInappropriate: false,
        severity: null,
        matchedRules: [],
        confidence: 0,
        explanation: 'Failed to parse AI response'
      };
    }
  }

  /**
   * Méthode publique pour modérer n'importe quel type de contenu
   */
  public async moderateContent(content: any, contentType: string): Promise<ModerationResult> {
    try {
      // Récupérer les règles de modération depuis l'API
      const rules = await this.getModerationRules(contentType);
      
      // Modérer le contenu en fonction de son type
      switch (contentType) {
        case 'TEXT':
        case 'COMMENT':
        case 'POST':
          return this.moderateText(content.text, rules);
        case 'IMAGE':
          return this.moderateImage(content.imageUrl, content.base64Image || '', rules);
        case 'VIDEO':
          // Pour les vidéos, on pourrait extraire des frames et les modérer comme des images
          // Mais pour l'instant, on renvoie un résultat par défaut
          return {
            isInappropriate: false,
            severity: null,
            matchedRules: [],
            confidence: 0,
            explanation: 'Video moderation not implemented yet'
          };
        default:
          throw new Error(`Unsupported content type: ${contentType}`);
      }
    } catch (error) {
      console.error('Error during content moderation:', error);
      throw new Error('Content moderation failed');
    }
  }

  /**
   * Récupère les règles de modération depuis l'API
   */
  private async getModerationRules(contentType: string): Promise<any[]> {
    try {
      // Déterminer le type de règles à récupérer
      const ruleType = contentType === 'IMAGE' || contentType === 'VIDEO' ? 'image' : 'text';
      
      // Appeler l'API pour récupérer les règles
      const response = await apiClient.get(`/moderation/${ruleType}/rules`);
      return response.data;
    } catch (error) {
      console.error('Error fetching moderation rules:', error);
      // Retourner des règles par défaut en cas d'erreur
      return [];
    }
  }
}

export const aiModerationService = new AIModerationService();
