import { AxiosError, AxiosRequestConfig, AxiosInstance, AxiosResponse } from 'axios';

// Configuration pour les tentatives de nouvelle requête
interface RetryConfig {
  maxRetries: number; // Nombre maximum de tentatives
  retryDelay: number; // <PERSON><PERSON><PERSON> initial entre les tentatives (en ms)
  retryStatusCodes: number[]; // Codes de statut HTTP à réessayer
  retryMethods: string[]; // Méthodes HTTP à réessayer
  backoffFactor: number; // Facteur d'augmentation du délai entre les tentatives
  shouldRetry: (error: AxiosError) => boolean; // Fonction personnalisée pour déterminer si une requête doit être réessayée
}

// Configuration par défaut
const defaultConfig: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000, // 1 seconde
  retryStatusCodes: [408, 429, 500, 502, 503, 504], // Timeout, Too Many Requests, Server Errors
  retryMethods: ['GET', 'HEAD', 'OPTIONS', 'PUT', 'DELETE'], // Exclure POST pour éviter les duplications
  backoffFactor: 2, // Backoff exponentiel
  shouldRetry: (error: AxiosError) => {
    // Vérifier si l'erreur a une réponse
    if (error.response) {
      // Vérifier si le code de statut est dans la liste des codes à réessayer
      return defaultConfig.retryStatusCodes.includes(error.response.status);
    }

    // Réessayer en cas d'erreur réseau (pas de réponse)
    return error.code === 'ECONNABORTED' || !error.response;
  },
};

// Fonction pour calculer le délai avant la prochaine tentative
const getRetryDelay = (retryCount: number, config: RetryConfig): number => {
  return config.retryDelay * Math.pow(config.backoffFactor, retryCount);
};

// Intercepteur d'erreur pour les tentatives de nouvelle requête
export const errorInterceptor = (
  error: AxiosError,
  axios: AxiosInstance,
  customConfig: Partial<RetryConfig> = {}
): Promise<AxiosResponse> => {
  // Fusionner la configuration personnalisée avec la configuration par défaut
  const config: RetryConfig = { ...defaultConfig, ...customConfig };

  // Obtenir la configuration de la requête
  const requestConfig = error.config as AxiosRequestConfig & { _retry?: number };

  // Initialiser le compteur de tentatives s'il n'existe pas
  if (requestConfig._retry === undefined) {
    requestConfig._retry = 0;
  }

  // Vérifier si nous devons réessayer la requête
  const shouldRetry =
    config.shouldRetry(error) &&
    requestConfig._retry < config.maxRetries &&
    config.retryMethods.includes(requestConfig.method?.toUpperCase() || '');

  if (shouldRetry) {
    // Incrémenter le compteur de tentatives
    requestConfig._retry++;

    // Calculer le délai avant la prochaine tentative
    const delay = getRetryDelay(requestConfig._retry, config);

    // Journaliser la tentative
    console.log(
      `Retry attempt ${requestConfig._retry}/${config.maxRetries} for ${requestConfig.url} in ${delay}ms`
    );

    // Attendre le délai puis réessayer la requête
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(axios(requestConfig));
      }, delay);
    });
  }

  // Si nous ne devons pas réessayer, rejeter la promesse avec l'erreur
  return Promise.reject(error);
};

// Fonction pour créer un intercepteur d'erreur avec une configuration personnalisée
export const createRetryInterceptor = (
  axios: AxiosInstance,
  customConfig: Partial<RetryConfig> = {}
) => {
  return (error: AxiosError) => errorInterceptor(error, axios, customConfig);
};
