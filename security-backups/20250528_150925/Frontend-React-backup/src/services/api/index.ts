// Base API client
export { apiClient } from './apiClient';

// Authentication service
export { authService } from './authService';
export type {
  LoginCredentials,
  RegistrationData,
  AuthResponse,
  ResetPasswordRequest,
} from './authService';

// User service
export { userService } from './userService';
export type { User, UserProfile, UpdateProfileRequest } from './userService';

// Retreat service
export { retreatService } from './retreatService';
export type {
  Retreat,
  RetreatFilters,
  CreateRetreatRequest,
  UpdateRetreatRequest,
  PaginatedRetreats,
} from './retreatService';

// Booking service
export { bookingService } from './bookingService';
export type {
  Booking,
  BookingFilters,
  CreateBookingRequest,
  UpdateBookingRequest,
  PaginatedBookings,
} from './bookingService';

// Partner service
export { partnerService } from './partnerService';
export type {
  Partner,
  PartnerDocument,
} from './partnerService';

// Matching service
export { matchingService } from './matchingService';
export type {
  MatchingCriteria,
  MatchingResponse,
  MatchingResult,
  PartnerMatch,
  RetreatMatch,
  CompatibilityFactors,
} from './matchingService';

// Social services
export { livestreamService } from './livestreamService';
export { blogService } from './blogService';
export { socialAnalyticsService } from './socialAnalyticsService';

// Agents services
export { agentsService } from './agentsService';
export { workflowService } from './workflowService';
export { metricsService } from './metricsService';
export { alertsService } from './alertsService';
export { reportsService } from './reportsService';

// Agents types
export type {
  AgentStatus,
  SystemMetrics,
  AgentConfig
} from './agentsService';

export type {
  Workflow,
  WorkflowStep,
  WorkflowTemplate,
  CreateWorkflowRequest
} from './workflowService';

export type {
  MetricData,
  ChartData,
  MetricDefinition
} from './metricsService';

export type {
  AlertItem,
  AlertAction,
  AlertRule
} from './alertsService';

export type {
  Report,
  ReportParameters,
  ReportTemplate,
  CreateReportRequest
} from './reportsService';
