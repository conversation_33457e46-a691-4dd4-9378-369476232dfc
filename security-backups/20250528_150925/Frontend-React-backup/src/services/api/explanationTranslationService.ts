import { apiClient } from './apiClient';

/**
 * Interface pour une traduction
 */
export interface Translation {
  /** Texte source */
  sourceText: string;
  
  /** Langue source */
  sourceLanguage: string;
  
  /** Texte traduit */
  translatedText: string;
  
  /** Langue cible */
  targetLanguage: string;
  
  /** Qualité de la traduction (0-1) */
  quality?: number;
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Interface pour un template d'explication traduit
 */
export interface TranslatedTemplate {
  /** ID du template original */
  originalId: string;
  
  /** Nom du template */
  name: string;
  
  /** Description du template */
  description: string;
  
  /** Type de facteur */
  factorType: string;
  
  /** Template traduit */
  template: string;
  
  /** Variables du template */
  variables: string[];
  
  /** Langue du template */
  language: string;
}

/**
 * Interface pour la requête de traduction de texte
 */
export interface TranslateTextRequest {
  /** Texte à traduire */
  text: string;
  
  /** Langue source */
  sourceLanguage: string;
  
  /** Langue cible */
  targetLanguage: string;
}

/**
 * Interface pour la requête de traduction de template
 */
export interface TranslateTemplateRequest {
  /** ID du template à traduire */
  templateId: string;
  
  /** Langue cible */
  targetLanguage: string;
}

/**
 * Interface pour la requête de traduction d'explication
 */
export interface TranslateExplanationRequest {
  /** ID de l'explication à traduire */
  explanationId: string;
  
  /** Langue cible */
  targetLanguage: string;
}

/**
 * Service pour la traduction des explications
 */
class ExplanationTranslationService {
  /**
   * Traduit un texte
   * @param request Requête de traduction
   * @returns Traduction
   */
  async translateText(request: TranslateTextRequest): Promise<Translation> {
    try {
      const response = await apiClient.post('/recommendation/explanation-translation/text', request);
      return response;
    } catch (error) {
      console.error('Erreur lors de la traduction du texte:', error);
      throw error;
    }
  }
  
  /**
   * Traduit un template d'explication
   * @param request Requête de traduction
   * @returns Template traduit
   */
  async translateTemplate(request: TranslateTemplateRequest): Promise<TranslatedTemplate> {
    try {
      const response = await apiClient.post('/recommendation/explanation-translation/template', request);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la traduction du template ${request.templateId}:`, error);
      throw error;
    }
  }
  
  /**
   * Traduit une explication
   * @param request Requête de traduction
   * @returns Explication traduite
   */
  async translateExplanation(request: TranslateExplanationRequest): Promise<any> {
    try {
      const response = await apiClient.post('/recommendation/explanation-translation/explanation', request);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la traduction de l'explication ${request.explanationId}:`, error);
      throw error;
    }
  }
  
  /**
   * Traduit tous les templates vers toutes les langues supportées
   * @returns Nombre de templates traduits
   */
  async translateAllTemplates(): Promise<{ count: number }> {
    try {
      const response = await apiClient.post('/recommendation/explanation-translation/translate-all-templates');
      return response;
    } catch (error) {
      console.error('Erreur lors de la traduction de tous les templates:', error);
      throw error;
    }
  }
  
  /**
   * Génère une explication dans la langue préférée de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @returns Explication générée
   */
  async generateExplanationInUserLanguage(recommendationId: string): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/explanation-translation/user-language-explanation', {
        params: { recommendationId },
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération de l'explication pour la recommandation ${recommendationId}:`, error);
      throw error;
    }
  }
  
  /**
   * Récupère les langues supportées
   * @returns Liste des langues supportées
   */
  getSupportedLanguages(): { code: string; name: string }[] {
    return [
      { code: 'fr', name: 'Français' },
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Español' },
      { code: 'de', name: 'Deutsch' },
      { code: 'it', name: 'Italiano' },
    ];
  }
  
  /**
   * Récupère le nom d'une langue à partir de son code
   * @param code Code de la langue
   * @returns Nom de la langue
   */
  getLanguageName(code: string): string {
    const language = this.getSupportedLanguages().find(lang => lang.code === code);
    return language ? language.name : code;
  }
}

export const explanationTranslationService = new ExplanationTranslationService();
