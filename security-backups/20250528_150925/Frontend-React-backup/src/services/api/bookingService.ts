import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

// Types pour les réservations
export interface Booking {
  id: string;
  retreatId: string;
  userId: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  numberOfGuests: number;
  totalPrice: number;
  currency: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  specialRequests?: string;
  retreat?: {
    id: string;
    title: string;
    location: string;
    startDate: string;
    endDate: string;
    images: string[];
  };
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profilePicture?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateBookingRequest {
  retreatId: string;
  numberOfGuests: number;
  specialRequests?: string;
}

export interface UpdateBookingRequest {
  status?: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  numberOfGuests?: number;
  specialRequests?: string;
}

export interface BookingFilters {
  status?: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedBookings {
  data: Booking[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Service de réservations pour gérer les opérations liées aux réservations
 */
export const bookingService = {
  /**
   * Obtenir la liste des réservations de l'utilisateur connecté
   * @param filters Filtres pour la recherche
   * @returns Liste paginée des réservations
   */
  async getMyBookings(filters: BookingFilters = {}): Promise<PaginatedBookings> {
    return apiClient.get<PaginatedBookings>(API_ENDPOINTS.BOOKING.USER_BOOKINGS, {
      params: filters,
    });
  },

  /**
   * Obtenir une réservation par son ID
   * @param id ID de la réservation
   * @returns Détails de la réservation
   */
  async getBookingById(id: string): Promise<Booking> {
    return apiClient.get<Booking>(`${API_ENDPOINTS.BOOKING.BASE}/${id}`);
  },

  /**
   * Créer une nouvelle réservation
   * @param bookingData Données de la réservation
   * @returns Réservation créée
   */
  async createBooking(bookingData: CreateBookingRequest): Promise<Booking> {
    return apiClient.post<Booking>(API_ENDPOINTS.BOOKING.BASE, bookingData);
  },

  /**
   * Mettre à jour une réservation
   * @param id ID de la réservation
   * @param bookingData Données à mettre à jour
   * @returns Réservation mise à jour
   */
  async updateBooking(id: string, bookingData: UpdateBookingRequest): Promise<Booking> {
    return apiClient.put<Booking>(`${API_ENDPOINTS.BOOKING.BASE}/${id}`, bookingData);
  },

  /**
   * Annuler une réservation
   * @param id ID de la réservation
   * @returns Réservation annulée
   */
  async cancelBooking(id: string): Promise<Booking> {
    return apiClient.post<Booking>(API_ENDPOINTS.BOOKING.CANCEL(id));
  },

  /**
   * Obtenir les réservations pour une retraite (pour les hôtes)
   * @param retreatId ID de la retraite
   * @param filters Filtres pour la recherche
   * @returns Liste paginée des réservations
   */
  async getBookingsForRetreat(
    retreatId: string,
    filters: BookingFilters = {}
  ): Promise<PaginatedBookings> {
    return apiClient.get<PaginatedBookings>(`${API_ENDPOINTS.RETREAT.BASE}/${retreatId}/bookings`, {
      params: filters,
    });
  },

  /**
   * Confirmer une réservation (pour les hôtes)
   * @param id ID de la réservation
   * @returns Réservation confirmée
   */
  async confirmBooking(id: string): Promise<Booking> {
    return apiClient.post<Booking>(API_ENDPOINTS.BOOKING.CONFIRM(id));
  },

  /**
   * Obtenir les réservations pour les retraites organisées par l'utilisateur connecté
   * @param filters Filtres pour la recherche
   * @returns Liste paginée des réservations
   */
  async getHostBookings(filters: BookingFilters = {}): Promise<PaginatedBookings> {
    return apiClient.get<PaginatedBookings>(API_ENDPOINTS.BOOKING.HOST_BOOKINGS, {
      params: filters,
    });
  },

  /**
   * Mettre à jour le statut d'une réservation
   * @param id ID de la réservation
   * @param status Nouveau statut de la réservation
   * @returns Réservation mise à jour
   */
  async updateBookingStatus(
    id: string,
    status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  ): Promise<Booking> {
    return apiClient.patch<Booking>(`${API_ENDPOINTS.BOOKING.BASE}/${id}/status`, { status });
  },
};
