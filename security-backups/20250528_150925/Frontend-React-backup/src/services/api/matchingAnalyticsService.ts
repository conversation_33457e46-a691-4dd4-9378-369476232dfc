import { apiClient } from './apiClient';
import { MatchingResult } from './matchingService';

export interface MatchingAnalyticsResponse {
  views: number;
  contacts: number;
  conversions: number;
  conversionRate: number;
  averageScore: number;
}

export interface MatchingDashboardData {
  matchingStats: {
    totalMatchings: number;
    averageScore: number;
    highQualityMatchings: number;
    conversionRate: number;
  };
  matchingTrends: Array<{
    date: string;
    matchings: number;
    conversions: number;
  }>;
  matchingDistribution: Array<{
    name: string;
    value: number;
  }>;
  partnerPerformance: Array<{
    name: string;
    matchings: number;
    conversions: number;
  }>;
}

export interface MatchingTrendsData {
  trends: Array<{
    date: string;
    matchings: number;
    views: number;
    contacts: number;
    conversions: number;
  }>;
}

export interface MatchingPerformanceData {
  executionTimes: Array<{
    date: string;
    averageExecutionTimeMs: number;
    maxExecutionTimeMs: number;
    requestCount: number;
  }>;
  errorRates: Array<{
    date: string;
    errorRate: number;
    requestCount: number;
    errorCount: number;
  }>;
}

class MatchingAnalyticsService {
  /**
   * Obtient les statistiques de matching pour un partenaire
   * @param partnerId ID du partenaire
   * @param period Période (day, week, month, year)
   * @returns Statistiques de matching
   */
  async getPartnerStats(
    partnerId: string,
    period: string = 'month',
  ): Promise<MatchingAnalyticsResponse> {
    try {
      const response = await apiClient.get<MatchingAnalyticsResponse>(`/matching/analytics/partner/${partnerId}?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error fetching partner matching stats:', error);
      throw error;
    }
  }

  /**
   * Enregistre une vue de matching
   * @param result Résultat du matching
   * @param metadata Métadonnées supplémentaires
   */
  async recordView(result: MatchingResult, metadata?: any): Promise<void> {
    try {
      await apiClient.post<void>('/matching/analytics/view', { result, metadata });
    } catch (error) {
      console.error('Error recording matching view:', error);
      // Ne pas propager l'erreur pour éviter de bloquer l'interface utilisateur
    }
  }

  /**
   * Enregistre un contact après matching
   * @param result Résultat du matching
   * @param contactMethod Méthode de contact
   * @param metadata Métadonnées supplémentaires
   */
  async recordContact(
    result: MatchingResult,
    contactMethod: string,
    metadata?: any,
  ): Promise<void> {
    try {
      await apiClient.post<void>('/matching/analytics/contact', { result, contactMethod, metadata });
    } catch (error) {
      console.error('Error recording matching contact:', error);
      // Ne pas propager l'erreur pour éviter de bloquer l'interface utilisateur
    }
  }

  /**
   * Enregistre une conversion après matching
   * @param result Résultat du matching
   * @param conversionType Type de conversion
   * @param metadata Métadonnées supplémentaires
   */
  async recordConversion(
    result: MatchingResult,
    conversionType: string,
    metadata?: any,
  ): Promise<void> {
    try {
      await apiClient.post<void>('/matching/analytics/conversion', { result, conversionType, metadata });
    } catch (error) {
      console.error('Error recording matching conversion:', error);
      // Ne pas propager l'erreur pour éviter de bloquer l'interface utilisateur
    }
  }

  /**
   * Obtient les données du tableau de bord d'analyse
   * @param period Période (day, week, month, year)
   * @returns Données du tableau de bord
   */
  async getDashboardData(period: string = 'month'): Promise<MatchingDashboardData> {
    try {
      const response = await apiClient.get<MatchingDashboardData>(`/matching/analytics/dashboard?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error fetching matching dashboard data:', error);
      
      // Retourner des données de démonstration en cas d'erreur
      return {
        matchingStats: {
          totalMatchings: 1245,
          averageScore: 78,
          highQualityMatchings: 532,
          conversionRate: 23.5,
        },
        matchingTrends: [
          { date: '2023-01', matchings: 120, conversions: 25 },
          { date: '2023-02', matchings: 150, conversions: 32 },
          { date: '2023-03', matchings: 180, conversions: 45 },
          { date: '2023-04', matchings: 210, conversions: 52 },
          { date: '2023-05', matchings: 250, conversions: 65 },
          { date: '2023-06', matchings: 280, conversions: 72 },
        ],
        matchingDistribution: [
          { name: '90-100%', value: 125 },
          { name: '80-89%', value: 286 },
          { name: '70-79%', value: 420 },
          { name: '60-69%', value: 314 },
          { name: '<60%', value: 100 },
        ],
        partnerPerformance: [
          { name: 'Yoga Studio', matchings: 85, conversions: 32 },
          { name: 'Wellness Center', matchings: 72, conversions: 28 },
          { name: 'Meditation Retreat', matchings: 65, conversions: 24 },
          { name: 'Spa Resort', matchings: 58, conversions: 18 },
          { name: 'Nutrition Coach', matchings: 45, conversions: 15 },
        ],
      };
    }
  }

  /**
   * Obtient les données de tendances
   * @param period Période (day, week, month, year)
   * @returns Données de tendances
   */
  async getTrendsData(period: string = 'month'): Promise<MatchingTrendsData> {
    try {
      const response = await apiClient.get<MatchingTrendsData>(`/matching/analytics/trends?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error fetching matching trends data:', error);
      
      // Retourner des données de démonstration en cas d'erreur
      return {
        trends: [
          { date: '2023-01', matchings: 120, views: 350, contacts: 80, conversions: 25 },
          { date: '2023-02', matchings: 150, views: 420, contacts: 95, conversions: 32 },
          { date: '2023-03', matchings: 180, views: 510, contacts: 120, conversions: 45 },
          { date: '2023-04', matchings: 210, views: 580, contacts: 140, conversions: 52 },
          { date: '2023-05', matchings: 250, views: 650, contacts: 160, conversions: 65 },
          { date: '2023-06', matchings: 280, views: 720, contacts: 190, conversions: 72 },
        ],
      };
    }
  }

  /**
   * Obtient les données de performance
   * @param period Période (day, week, month, year)
   * @returns Données de performance
   */
  async getPerformanceData(period: string = 'month'): Promise<MatchingPerformanceData> {
    try {
      const response = await apiClient.get<MatchingPerformanceData>(`/matching/analytics/performance?period=${period}`);
      return response;
    } catch (error) {
      console.error('Error fetching matching performance data:', error);
      
      // Retourner des données de démonstration en cas d'erreur
      return {
        executionTimes: [
          { date: '2023-01', averageExecutionTimeMs: 120, maxExecutionTimeMs: 350, requestCount: 1200 },
          { date: '2023-02', averageExecutionTimeMs: 115, maxExecutionTimeMs: 320, requestCount: 1350 },
          { date: '2023-03', averageExecutionTimeMs: 105, maxExecutionTimeMs: 290, requestCount: 1500 },
          { date: '2023-04', averageExecutionTimeMs: 95, maxExecutionTimeMs: 270, requestCount: 1650 },
          { date: '2023-05', averageExecutionTimeMs: 90, maxExecutionTimeMs: 250, requestCount: 1800 },
          { date: '2023-06', averageExecutionTimeMs: 85, maxExecutionTimeMs: 230, requestCount: 1950 },
        ],
        errorRates: [
          { date: '2023-01', errorRate: 2.5, requestCount: 1200, errorCount: 30 },
          { date: '2023-02', errorRate: 2.2, requestCount: 1350, errorCount: 30 },
          { date: '2023-03', errorRate: 1.8, requestCount: 1500, errorCount: 27 },
          { date: '2023-04', errorRate: 1.5, requestCount: 1650, errorCount: 25 },
          { date: '2023-05', errorRate: 1.2, requestCount: 1800, errorCount: 22 },
          { date: '2023-06', errorRate: 1.0, requestCount: 1950, errorCount: 20 },
        ],
      };
    }
  }
}

export const matchingAnalyticsService = new MatchingAnalyticsService();
