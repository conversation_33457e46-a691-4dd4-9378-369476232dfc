import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

// Define types for the responses
interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  metadata: Record<string, string>;
}

interface PaymentMethod {
  id: string;
  type: string;
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  metadata: Record<string, string>;
}

interface Refund {
  id: string;
  amount: number;
  status: string;
  created: number;
  metadata: Record<string, string>;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  pdfUrl: string;
}

interface PromoCodeResponse {
  discountedAmount: number;
  discountAmount: number;
  promoCode: string;
}

/**
 * Service for handling payment operations
 */
export const paymentService = {
  /**
   * Create a payment intent for a booking
   * @param bookingId - ID of the booking to pay for
   * @returns Payment intent with client secret
   */
  createPaymentIntent: async (bookingId: string): Promise<PaymentIntent> => {
    const response = await apiClient.post<PaymentIntent>(
      `${API_ENDPOINTS.FINANCIAL.PAYMENT_INTENT}`,
      { bookingId }
    );
    return response;
  },

  /**
   * Confirm a payment
   * @param paymentIntentId - ID of the payment intent to confirm
   * @param paymentMethodId - ID of the payment method to use
   * @returns Confirmed payment
   */
  confirmPayment: async (paymentIntentId: string, paymentMethodId: string): Promise<Payment> => {
    const response = await apiClient.post<Payment>(`${API_ENDPOINTS.FINANCIAL.CONFIRM_PAYMENT}`, {
      paymentIntentId,
      paymentMethodId,
    });
    return response;
  },

  /**
   * Get payment methods for the current user
   * @returns List of payment methods
   */
  getPaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await apiClient.get<PaymentMethod[]>(
      `${API_ENDPOINTS.FINANCIAL.PAYMENT_METHODS}`
    );
    return response;
  },

  /**
   * Add a new payment method for the current user
   * @param paymentMethodId - ID of the payment method to add
   * @returns Added payment method
   */
  addPaymentMethod: async (paymentMethodId: string): Promise<PaymentMethod> => {
    const response = await apiClient.post<PaymentMethod>(
      `${API_ENDPOINTS.FINANCIAL.PAYMENT_METHODS}`,
      { paymentMethodId }
    );
    return response;
  },

  /**
   * Delete a payment method
   * @param paymentMethodId - ID of the payment method to delete
   * @returns Success status
   */
  deletePaymentMethod: async (paymentMethodId: string): Promise<{ success: boolean }> => {
    const response = await apiClient.delete<{ success: boolean }>(
      `${API_ENDPOINTS.FINANCIAL.PAYMENT_METHODS}/${paymentMethodId}`
    );
    return response;
  },

  /**
   * Get payment history for the current user
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Paginated list of payments
   */
  getPaymentHistory: async (
    page = 1,
    limit = 10
  ): Promise<{ data: Payment[]; total: number; page: number; limit: number }> => {
    const response = await apiClient.get<{
      data: Payment[];
      total: number;
      page: number;
      limit: number;
    }>(`${API_ENDPOINTS.FINANCIAL.PAYMENT_HISTORY}?page=${page}&limit=${limit}`);
    return response;
  },

  /**
   * Get details of a specific payment
   * @param paymentId - ID of the payment to get details for
   * @returns Payment details
   */
  getPaymentDetails: async (paymentId: string): Promise<Payment> => {
    const response = await apiClient.get<Payment>(
      `${API_ENDPOINTS.FINANCIAL.PAYMENTS}/${paymentId}`
    );
    return response;
  },

  /**
   * Request a refund for a payment
   * @param paymentId - ID of the payment to refund
   * @param amount - Amount to refund (optional, full amount if not specified)
   * @param reason - Reason for the refund
   * @returns Refund details
   */
  requestRefund: async (paymentId: string, amount?: number, reason?: string): Promise<Refund> => {
    const response = await apiClient.post<Refund>(`${API_ENDPOINTS.FINANCIAL.REFUNDS}`, {
      paymentId,
      amount,
      reason,
    });
    return response;
  },

  /**
   * Get available payment methods for a specific country
   * @param countryCode - ISO country code
   * @returns List of available payment methods
   */
  getAvailablePaymentMethods: async (countryCode: string): Promise<string[]> => {
    const response = await apiClient.get<string[]>(
      `${API_ENDPOINTS.FINANCIAL.AVAILABLE_PAYMENT_METHODS}?country=${countryCode}`
    );
    return response;
  },

  /**
   * Apply a promo code to a booking
   * @param bookingId - ID of the booking
   * @param promoCode - Promo code to apply
   * @returns Updated booking with applied discount
   */
  applyPromoCode: async (bookingId: string, promoCode: string): Promise<PromoCodeResponse> => {
    const response = await apiClient.post<PromoCodeResponse>(
      `${API_ENDPOINTS.FINANCIAL.PROMO_CODES}/apply`,
      { bookingId, promoCode }
    );
    return response;
  },

  /**
   * Get invoice for a payment
   * @param paymentId - ID of the payment
   * @returns Invoice details
   */
  getInvoice: async (paymentId: string): Promise<Invoice> => {
    const response = await apiClient.get<Invoice>(
      `${API_ENDPOINTS.FINANCIAL.INVOICES}/payment/${paymentId}`
    );
    return response;
  },

  /**
   * Download invoice as PDF
   * @param invoiceId - ID of the invoice
   * @returns PDF blob
   */
  downloadInvoice: async (invoiceId: string): Promise<Blob> => {
    const response = await apiClient.get<Blob>(
      `${API_ENDPOINTS.FINANCIAL.INVOICES}/${invoiceId}/download`,
      { responseType: 'blob' }
    );
    return response;
  },
};
