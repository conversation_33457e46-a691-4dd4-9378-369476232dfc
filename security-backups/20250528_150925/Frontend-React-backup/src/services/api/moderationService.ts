import { apiClient } from './apiClient';

export interface ModerationResult {
  isInappropriate: boolean;
  severity: string | null;
  matchedRules: {
    id: string;
    name: string;
    pattern?: string;
    category?: string;
    threshold?: number;
    severity: string;
  }[];
  confidence: number;
  categories?: Record<string, number>;
}

export interface Report {
  id: string;
  contentType: string;
  contentId: string;
  reporterId: string;
  reason: string;
  description?: string;
  status: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'ESCALATED';
  createdAt: string;
  updatedAt: string;
  moderationActions: ModerationAction[];
}

export interface ModerationAction {
  id: string;
  reportId: string;
  moderatorId: string;
  action: string;
  comment?: string;
  createdAt: string;
}

export interface ReportFilters {
  status?: string;
  contentType?: string;
  reporterId?: string;
  skip?: number;
  take?: number;
}

export interface ModerationStats {
  total: number;
  pending: number;
  inReview: number;
  approved: number;
  rejected: number;
  escalated: number;
}

export interface TextModerationRule {
  id: string;
  name: string;
  description?: string;
  pattern: string;
  severity: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ImageModerationRule {
  id: string;
  name: string;
  description?: string;
  category: string;
  threshold: number;
  severity: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

class ModerationService {
  async moderateContent(content: any, contentType: string): Promise<ModerationResult> {
    const response = await apiClient.post(`/moderation/content?contentType=${contentType}`, content);
    return response.data;
  }

  async getReports(filters: ReportFilters = {}): Promise<{ reports: Report[]; total: number }> {
    const queryParams = new URLSearchParams();
    
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.contentType) queryParams.append('contentType', filters.contentType);
    if (filters.reporterId) queryParams.append('reporterId', filters.reporterId);
    if (filters.skip !== undefined) queryParams.append('skip', filters.skip.toString());
    if (filters.take !== undefined) queryParams.append('take', filters.take.toString());
    
    const response = await apiClient.get(`/moderation/reports?${queryParams.toString()}`);
    return response.data;
  }

  async getReport(id: string): Promise<Report> {
    const response = await apiClient.get(`/moderation/reports/${id}`);
    return response.data;
  }

  async createReport(reportData: {
    contentType: string;
    contentId: string;
    reporterId: string;
    reason: string;
    description?: string;
  }): Promise<Report> {
    const response = await apiClient.post('/moderation/reports', reportData);
    return response.data;
  }

  async updateReport(id: string, updateData: {
    status?: string;
    description?: string;
  }): Promise<Report> {
    const response = await apiClient.put(`/moderation/reports/${id}`, updateData);
    return response.data;
  }

  async addModerationAction(reportId: string, actionData: {
    moderatorId: string;
    action: string;
    comment?: string;
  }): Promise<ModerationAction> {
    const response = await apiClient.post(`/moderation/reports/${reportId}/actions`, actionData);
    return response.data;
  }

  async getStats(): Promise<ModerationStats> {
    const response = await apiClient.get('/moderation/stats');
    return response.data;
  }

  async getTextRules(): Promise<TextModerationRule[]> {
    const response = await apiClient.get('/moderation/text/rules');
    return response.data;
  }

  async createTextRule(ruleData: {
    name: string;
    description?: string;
    pattern: string;
    severity: string;
    isActive?: boolean;
  }): Promise<TextModerationRule> {
    const response = await apiClient.post('/moderation/text/rules', ruleData);
    return response.data;
  }

  async updateTextRule(id: string, ruleData: {
    name?: string;
    description?: string;
    pattern?: string;
    severity?: string;
    isActive?: boolean;
  }): Promise<TextModerationRule> {
    const response = await apiClient.put(`/moderation/text/rules/${id}`, ruleData);
    return response.data;
  }

  async deleteTextRule(id: string): Promise<void> {
    await apiClient.delete(`/moderation/text/rules/${id}`);
  }

  async getImageRules(): Promise<ImageModerationRule[]> {
    const response = await apiClient.get('/moderation/image/rules');
    return response.data;
  }

  async createImageRule(ruleData: {
    name: string;
    description?: string;
    category: string;
    threshold: number;
    severity: string;
    isActive?: boolean;
  }): Promise<ImageModerationRule> {
    const response = await apiClient.post('/moderation/image/rules', ruleData);
    return response.data;
  }

  async updateImageRule(id: string, ruleData: {
    name?: string;
    description?: string;
    category?: string;
    threshold?: number;
    severity?: string;
    isActive?: boolean;
  }): Promise<ImageModerationRule> {
    const response = await apiClient.put(`/moderation/image/rules/${id}`, ruleData);
    return response.data;
  }

  async deleteImageRule(id: string): Promise<void> {
    await apiClient.delete(`/moderation/image/rules/${id}`);
  }
}

export const moderationService = new ModerationService();
