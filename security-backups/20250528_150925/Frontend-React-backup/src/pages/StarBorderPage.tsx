import { StarBorder } from '../components/ui/star-border';

export default function StarBorderPage() {
  return (
    <div className='min-h-screen bg-background p-8'>
      <div className='max-w-2xl mx-auto space-y-8'>
        <h1 className='text-3xl font-bold'>StarBorder Demo</h1>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>Basic Usage</h2>
          <StarBorder>Theme-aware Border</StarBorder>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>Custom Color</h2>
          <StarBorder color='#53a271'>Custom Color Border</StarBorder>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>Custom Speed</h2>
          <StarBorder speed='3s'>Faster Animation</StarBorder>
        </div>

        <div className='space-y-4'>
          <h2 className='text-xl font-semibold'>As Link</h2>
          <StarBorder as='a' href='#'>
            Link with Border
          </StarBorder>
        </div>
      </div>
    </div>
  );
}
