import React from 'react';
import { Helmet } from 'react-helmet-async';
import { RegistrationForm } from '../components/auth/RegistrationForm';
import MainLayout from '../components/templates/MainLayout/MainLayout'; // Assuming a MainLayout exists
import { useAuth } from '../contexts/AuthContext';
import { Navigate, Link } from 'react-router-dom';

const RegistrationPage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to='/' replace />; // Redirect if already logged in
  }

  return (
    <MainLayout>
      <Helmet>
        <title>Inscription | Retreat and Be</title>
        <meta name='description' content='Créez votre compte Retreat and Be pour commencer votre voyage de bien-être.' />
      </Helmet>
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
        <div className='w-full max-w-md'>
          <RegistrationForm />
          <p className='mt-8 text-center text-sm text-gray-600'>
            Déjà un compte ? <Link to='/login' className='font-medium text-indigo-600 hover:text-indigo-500'>Connectez-vous ici</Link>
          </p>
        </div>
      </div>
    </MainLayout>
  );
};

export default RegistrationPage; 