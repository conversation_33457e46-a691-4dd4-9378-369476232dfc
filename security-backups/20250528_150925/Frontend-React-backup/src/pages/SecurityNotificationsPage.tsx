import React from 'react';
import { Helmet } from 'react-helmet-async';
import { SecurityNotifications } from '../components/security';
import MainLayout from '../components/templates/MainLayout/MainLayout';
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const SecurityNotificationsPage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  // Vérifier si l'utilisateur est authentifié
  if (!isAuthenticated) {
    return <Navigate to='/login' replace />;
  }

  return (
    <MainLayout>
      <Helmet>
        <title>Notifications de sécurité | Retreat and Be</title>
        <meta
          name='description'
          content='Consultez vos notifications de sécurité et restez informé des événements importants concernant votre compte.'
        />
      </Helmet>

      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-2xl font-bold text-gray-900 mb-6'>Notifications de sécurité</h1>
        <p className='text-gray-600 mb-8'>
          Consultez vos notifications de sécurité et restez informé des événements importants
          concernant votre compte et vos activités sur la plateforme.
        </p>

        <SecurityNotifications showAll={true} />
      </div>
    </MainLayout>
  );
};

export default SecurityNotificationsPage;
