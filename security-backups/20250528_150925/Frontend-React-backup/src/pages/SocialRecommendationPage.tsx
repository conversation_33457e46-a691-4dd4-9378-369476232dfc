import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import { socialGroupRecommendationService } from '../services/api/socialGroupRecommendationService';
import { toast } from 'react-toastify';
import { t } from '../services/i18n/i18nService';
import NavBar from '../components/organisms/NavBar/NavBar';
import Footer from '../components/organisms/Footer/Footer';
import RetreatCard from '../components/retreats/RetreatCard';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * Page pour afficher les recommandations sociales
 */
const SocialRecommendationPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [socialFactor, setSocialFactor] = useState<number>(0.7);
  const [relationTypes, setRelationTypes] = useState<Array<'friend' | 'follower' | 'following' | 'colleague' | 'family'>>([
    'friend', 'follower', 'following', 'colleague', 'family',
  ]);
  const [networkDepth, setNetworkDepth] = useState<number>(2);
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: '/social' } });
    }
  }, [user, navigate]);
  
  // Charger les recommandations sociales
  useEffect(() => {
    const loadRecommendations = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const socialRecommendations = await socialGroupRecommendationService.getSocialRecommendations({
          socialFactor,
          maxRecommendations: 12,
          relationTypes,
          networkDepth,
        });
        
        setRecommendations(socialRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations sociales:', error);
        setError(t('social.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadRecommendations();
    }
  }, [user, socialFactor, relationTypes, networkDepth]);
  
  // Gérer le changement de facteur social
  const handleSocialFactorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSocialFactor(parseFloat(e.target.value));
  };
  
  // Gérer le changement de types de relation
  const handleRelationTypeChange = (type: 'friend' | 'follower' | 'following' | 'colleague' | 'family') => {
    if (relationTypes.includes(type)) {
      setRelationTypes(relationTypes.filter(t => t !== type));
    } else {
      setRelationTypes([...relationTypes, type]);
    }
  };
  
  // Gérer le changement de profondeur du réseau
  const handleNetworkDepthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setNetworkDepth(parseInt(e.target.value, 10));
  };
  
  // Gérer le rafraîchissement des recommandations
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const socialRecommendations = await socialGroupRecommendationService.getSocialRecommendations({
        socialFactor,
        maxRecommendations: 12,
        relationTypes,
        networkDepth,
      });
      
      setRecommendations(socialRecommendations);
      
      toast.success(t('social.refreshSuccess'));
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des recommandations sociales:', error);
      setError(t('social.refreshError'));
      toast.error(t('social.refreshError'));
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>{t('social.pageTitle')} | Retreat And Be</title>
        <meta
          name="description"
          content={t('social.pageDescription')}
        />
      </Helmet>
      
      <NavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('social.title')}</h1>
            <p className="text-lg text-gray-600">{t('social.description')}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('social.preferences')}</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('social.socialFactor')} ({Math.round(socialFactor * 100)}%)
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={socialFactor}
                onChange={handleSocialFactorChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{t('social.lessSocial')}</span>
                <span>{t('social.moreSocial')}</span>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('social.relationTypes')}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={relationTypes.includes('friend')}
                    onChange={() => handleRelationTypeChange('friend')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('social.relationTypes.friend')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={relationTypes.includes('follower')}
                    onChange={() => handleRelationTypeChange('follower')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('social.relationTypes.follower')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={relationTypes.includes('following')}
                    onChange={() => handleRelationTypeChange('following')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('social.relationTypes.following')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={relationTypes.includes('colleague')}
                    onChange={() => handleRelationTypeChange('colleague')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('social.relationTypes.colleague')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={relationTypes.includes('family')}
                    onChange={() => handleRelationTypeChange('family')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('social.relationTypes.family')}</span>
                </label>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('social.networkDepth')}
              </label>
              <select
                value={networkDepth}
                onChange={handleNetworkDepthChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm rounded-md"
              >
                <option value={1}>{t('social.networkDepth.direct')}</option>
                <option value={2}>{t('social.networkDepth.friends')}</option>
                <option value={3}>{t('social.networkDepth.extended')}</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">
                {networkDepth === 1 && t('social.networkDepth.directDescription')}
                {networkDepth === 2 && t('social.networkDepth.friendsDescription')}
                {networkDepth === 3 && t('social.networkDepth.extendedDescription')}
              </p>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
              >
                {t('social.refresh')}
              </button>
            </div>
          </div>
          
          {/* Contenu principal */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('social.tryAgain')}
                </button>
              </div>
            </div>
          ) : recommendations.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('social.noRecommendations')}</h3>
                <p className="text-gray-500 mb-6">{t('social.connectWithOthers')}</p>
                
                <button
                  onClick={() => navigate('/connections')}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('social.findConnections')}
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.map((recommendation) => (
                <RetreatCard
                  key={recommendation.id}
                  retreat={recommendation}
                  isSocial={true}
                  socialSources={recommendation.socialSources}
                />
              ))}
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialRecommendationPage;
