import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import { EnvelopeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';

const ResetPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Ici, ajoutez la logique d'envoi du mail de réinitialisation
    setIsSubmitted(true);
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <NavBarClient />

      <main className='pt-24 pb-16'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='max-w-md mx-auto'>
            <Link
              to='/auth'
              className='inline-flex items-center text-sm text-retreat-green hover:text-retreat-green-dark mb-6'
            >
              <ArrowLeftIcon className='h-4 w-4 mr-2' />
              Retour à la connexion
            </Link>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className='bg-white rounded-xl shadow-lg overflow-hidden'
            >
              {!isSubmitted ? (
                <>
                  <div className='p-6 bg-retreat-green text-center'>
                    <h1 className='text-2xl font-bold text-white'>
                      Réinitialisation du mot de passe
                    </h1>
                  </div>

                  <div className='p-6'>
                    <p className='text-gray-600 mb-6'>
                      Entrez votre adresse e-mail ci-dessous. Nous vous enverrons un lien pour
                      réinitialiser votre mot de passe.
                    </p>

                    <form onSubmit={handleSubmit} className='space-y-6'>
                      <div>
                        <label
                          htmlFor='resetEmailInput'
                          className='block text-sm font-medium text-gray-700'
                        >
                          Email
                        </label>
                        <div className='mt-1 relative'>
                          <input
                            id='resetEmailInput'
                            type='email'
                            required
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className='block w-full rounded-lg border-gray-300 pl-10 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                            placeholder='<EMAIL>'
                          />
                          <EnvelopeIcon className='h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2' />
                        </div>
                      </div>

                      <button
                        type='submit'
                        className='w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green'
                      >
                        Envoyer le lien de réinitialisation
                      </button>
                    </form>
                  </div>
                </>
              ) : (
                <div className='p-6'>
                  <div className='text-center'>
                    <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4'>
                      <svg
                        className='h-6 w-6 text-green-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth='2'
                          d='M5 13l4 4L19 7'
                        />
                      </svg>
                    </div>
                    <h2 className='text-lg font-medium text-gray-900 mb-2'>Email envoyé !</h2>
                    <p className='text-gray-600'>
                      Si un compte existe avec l&apos;adresse {email}, vous recevrez un email
                      contenant les instructions pour réinitialiser votre mot de passe.
                    </p>
                    <Link
                      to='/auth'
                      className='mt-6 inline-block text-retreat-green hover:text-retreat-green-dark'
                    >
                      Retour à la page de connexion
                    </Link>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ResetPasswordPage;
