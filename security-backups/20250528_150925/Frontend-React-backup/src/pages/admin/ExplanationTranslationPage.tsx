import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { 
  explanationTranslationService,
  Translation,
  TranslatedTemplate,
  TranslateTextRequest,
  TranslateTemplateRequest,
} from '../../services/api/explanationTranslationService';
import { t } from '../../services/i18n/i18nService';

/**
 * Page d'administration pour la traduction des explications
 */
const ExplanationTranslationPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(false);
  const [translatingAll, setTranslatingAll] = useState<boolean>(false);
  const [textToTranslate, setTextToTranslate] = useState<string>('');
  const [sourceLanguage, setSourceLanguage] = useState<string>('fr');
  const [targetLanguage, setTargetLanguage] = useState<string>('en');
  const [translation, setTranslation] = useState<Translation | null>(null);
  const [templateId, setTemplateId] = useState<string>('');
  const [translatedTemplate, setTranslatedTemplate] = useState<TranslatedTemplate | null>(null);
  const [activeTab, setActiveTab] = useState<'text' | 'template' | 'batch'>('text');
  
  const supportedLanguages = explanationTranslationService.getSupportedLanguages();
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Gérer la traduction de texte
  const handleTranslateText = async () => {
    if (!textToTranslate) {
      toast.error('Veuillez saisir un texte à traduire');
      return;
    }
    
    try {
      setLoading(true);
      
      const request: TranslateTextRequest = {
        text: textToTranslate,
        sourceLanguage,
        targetLanguage,
      };
      
      const result = await explanationTranslationService.translateText(request);
      setTranslation(result);
      
      toast.success('Texte traduit avec succès');
    } catch (error) {
      console.error('Erreur lors de la traduction du texte:', error);
      toast.error('Erreur lors de la traduction du texte');
    } finally {
      setLoading(false);
    }
  };
  
  // Gérer la traduction de template
  const handleTranslateTemplate = async () => {
    if (!templateId) {
      toast.error('Veuillez saisir un ID de template');
      return;
    }
    
    try {
      setLoading(true);
      
      const request: TranslateTemplateRequest = {
        templateId,
        targetLanguage,
      };
      
      const result = await explanationTranslationService.translateTemplate(request);
      setTranslatedTemplate(result);
      
      toast.success('Template traduit avec succès');
    } catch (error) {
      console.error('Erreur lors de la traduction du template:', error);
      toast.error('Erreur lors de la traduction du template');
    } finally {
      setLoading(false);
    }
  };
  
  // Gérer la traduction de tous les templates
  const handleTranslateAllTemplates = async () => {
    try {
      setTranslatingAll(true);
      
      const result = await explanationTranslationService.translateAllTemplates();
      
      toast.success(`${result.count} templates traduits avec succès`);
    } catch (error) {
      console.error('Erreur lors de la traduction de tous les templates:', error);
      toast.error('Erreur lors de la traduction de tous les templates');
    } finally {
      setTranslatingAll(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Traduction des Explications | Retreat And Be</title>
        <meta
          name="description"
          content="Gestion des traductions des explications pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Traduction des Explications</h1>
            <p className="text-gray-600">Gérer les traductions des explications pour différentes langues</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'text'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('text')}
                  >
                    Traduction de texte
                  </button>
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'template'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('template')}
                  >
                    Traduction de template
                  </button>
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'batch'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('batch')}
                  >
                    Traduction par lot
                  </button>
                </nav>
              </div>
              
              <div className="mt-6">
                {activeTab === 'text' && (
                  <div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Texte à traduire
                        </label>
                        <textarea
                          value={textToTranslate}
                          onChange={(e) => setTextToTranslate(e.target.value)}
                          className="w-full h-40 rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                          placeholder="Saisissez le texte à traduire..."
                          disabled={loading}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Traduction
                        </label>
                        <textarea
                          value={translation?.translatedText || ''}
                          readOnly
                          className="w-full h-40 rounded-md border-gray-300 bg-gray-50 shadow-sm"
                          placeholder="La traduction apparaîtra ici..."
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Langue source
                        </label>
                        <select
                          value={sourceLanguage}
                          onChange={(e) => setSourceLanguage(e.target.value)}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                          disabled={loading}
                        >
                          {supportedLanguages.map((language) => (
                            <option key={language.code} value={language.code}>
                              {language.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Langue cible
                        </label>
                        <select
                          value={targetLanguage}
                          onChange={(e) => setTargetLanguage(e.target.value)}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                          disabled={loading}
                        >
                          {supportedLanguages.map((language) => (
                            <option key={language.code} value={language.code}>
                              {language.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    <div className="mt-6 flex justify-end">
                      <button
                        onClick={handleTranslateText}
                        className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                        disabled={loading || !textToTranslate}
                      >
                        {loading ? 'Traduction en cours...' : 'Traduire'}
                      </button>
                    </div>
                    
                    {translation && (
                      <div className="mt-6 p-4 bg-gray-50 rounded-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Informations sur la traduction</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <span className="text-gray-500">Langue source:</span>
                            <span className="ml-2">{explanationTranslationService.getLanguageName(translation.sourceLanguage)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Langue cible:</span>
                            <span className="ml-2">{explanationTranslationService.getLanguageName(translation.targetLanguage)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Qualité:</span>
                            <span className="ml-2">{translation.quality ? `${Math.round(translation.quality * 100)}%` : 'N/A'}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Fournisseur:</span>
                            <span className="ml-2">{translation.metadata?.provider || 'N/A'}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {activeTab === 'template' && (
                  <div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          ID du template
                        </label>
                        <input
                          type="text"
                          value={templateId}
                          onChange={(e) => setTemplateId(e.target.value)}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                          placeholder="Saisissez l'ID du template..."
                          disabled={loading}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Langue cible
                        </label>
                        <select
                          value={targetLanguage}
                          onChange={(e) => setTargetLanguage(e.target.value)}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                          disabled={loading}
                        >
                          {supportedLanguages.map((language) => (
                            <option key={language.code} value={language.code}>
                              {language.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    <div className="mt-6 flex justify-end">
                      <button
                        onClick={handleTranslateTemplate}
                        className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                        disabled={loading || !templateId}
                      >
                        {loading ? 'Traduction en cours...' : 'Traduire le template'}
                      </button>
                    </div>
                    
                    {translatedTemplate && (
                      <div className="mt-6 p-4 bg-gray-50 rounded-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Template traduit</h3>
                        <div className="grid grid-cols-1 gap-4">
                          <div>
                            <span className="text-gray-500">Nom:</span>
                            <span className="ml-2">{translatedTemplate.name}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Description:</span>
                            <span className="ml-2">{translatedTemplate.description}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Type de facteur:</span>
                            <span className="ml-2">{translatedTemplate.factorType}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Langue:</span>
                            <span className="ml-2">{explanationTranslationService.getLanguageName(translatedTemplate.language)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Template:</span>
                            <div className="mt-2 p-3 bg-white border border-gray-200 rounded-md">
                              {translatedTemplate.template}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Variables:</span>
                            <div className="mt-2 flex flex-wrap gap-2">
                              {translatedTemplate.variables.map((variable, index) => (
                                <span key={index} className="px-2 py-1 bg-gray-200 rounded-md text-xs">
                                  {variable}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {activeTab === 'batch' && (
                  <div>
                    <div className="bg-gray-50 p-6 rounded-md">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Traduction par lot</h3>
                      <p className="text-gray-600 mb-4">
                        Cette action va traduire tous les templates d'explication vers toutes les langues supportées.
                        Cela peut prendre un certain temps en fonction du nombre de templates.
                      </p>
                      
                      <div className="mt-6 flex justify-end">
                        <button
                          onClick={handleTranslateAllTemplates}
                          className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                          disabled={translatingAll}
                        >
                          {translatingAll ? 'Traduction en cours...' : 'Traduire tous les templates'}
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-blue-700">
                            Les templates sont automatiquement traduits lorsqu'ils sont créés ou mis à jour.
                            Utilisez cette fonction uniquement si vous avez besoin de retraduire tous les templates existants.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ExplanationTranslationPage;
