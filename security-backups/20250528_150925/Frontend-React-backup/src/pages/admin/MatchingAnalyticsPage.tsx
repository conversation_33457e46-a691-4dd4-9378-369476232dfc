import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../hooks/useAuthContext';
import Navbar from '../../components/organisms/Navbar/Navbar';
import Footer from '../../components/organisms/Footer/Footer';
import MatchingAnalyticsDashboard from '../../components/admin/MatchingAnalyticsDashboard';

const MatchingAnalyticsPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [activePeriod, setActivePeriod] = useState<'day' | 'week' | 'month' | 'year'>('month');

  // Rediriger si l'utilisateur n'est pas connecté ou n'est pas administrateur
  React.useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (user.role !== 'ADMIN') {
      navigate('/');
      return;
    }
  }, [user, navigate]);

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Analyse des Matchings | Retreat And Be</title>
        <meta
          name="description"
          content="Tableau de bord d'analyse des matchings pour les administrateurs de Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Analyse des Matchings</h1>
            <p className="mt-1 text-sm text-gray-500">
              Tableau de bord d'analyse des matchings entre partenaires et retraites.
            </p>
          </div>

          <div className="mb-6">
            <div className="bg-white rounded-lg shadow-md p-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Filtres</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-1">
                    Période
                  </label>
                  <select
                    id="period"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                    value={activePeriod}
                    onChange={(e) => setActivePeriod(e.target.value as any)}
                  >
                    <option value="day">Jour</option>
                    <option value="week">Semaine</option>
                    <option value="month">Mois</option>
                    <option value="year">Année</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="partner" className="block text-sm font-medium text-gray-700 mb-1">
                    Partenaire
                  </label>
                  <select
                    id="partner"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                  >
                    <option value="">Tous les partenaires</option>
                    <option value="1">Yoga Studio</option>
                    <option value="2">Wellness Center</option>
                    <option value="3">Meditation Retreat</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                    Catégorie
                  </label>
                  <select
                    id="category"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                  >
                    <option value="">Toutes les catégories</option>
                    <option value="WELLNESS">Bien-être</option>
                    <option value="GUIDE">Guide</option>
                    <option value="TRANSPORT">Transport</option>
                    <option value="CATERING">Restauration</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="score" className="block text-sm font-medium text-gray-700 mb-1">
                    Score minimum
                  </label>
                  <select
                    id="score"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                  >
                    <option value="0">Tous les scores</option>
                    <option value="60">60% et plus</option>
                    <option value="70">70% et plus</option>
                    <option value="80">80% et plus</option>
                    <option value="90">90% et plus</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <MatchingAnalyticsDashboard period={activePeriod} />

          <div className="mt-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Recommandations</h2>
              <div className="space-y-4">
                <div className="bg-green-50 border-l-4 border-green-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-green-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-green-700">
                        Le taux de conversion a augmenté de 15% ce mois-ci. Continuez à améliorer la qualité des matchings.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-yellow-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        Les matchings dans la catégorie "Transport" ont un taux de conversion inférieur à la moyenne. Envisagez d'ajuster l'algorithme pour cette catégorie.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        Les partenaires certifiés ont un taux de conversion 30% plus élevé que les partenaires standard. Encouragez plus de partenaires à obtenir la certification.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default MatchingAnalyticsPage;
