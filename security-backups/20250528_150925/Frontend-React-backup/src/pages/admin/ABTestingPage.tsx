import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';

/**
 * Interface pour un test A/B
 */
interface ABTest {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string | null;
  status: 'DRAFT' | 'RUNNING' | 'PAUSED' | 'COMPLETED';
  variants: number;
  participants: number;
  targetAudience: string;
  createdBy: string;
}

/**
 * Page de gestion des tests A/B
 */
const ABTestingPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tests, setTests] = useState<ABTest[]>([]);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [newTest, setNewTest] = useState<{
    name: string;
    description: string;
    targetAudience: string;
  }>({
    name: '',
    description: '',
    targetAudience: 'all',
  });
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger les tests A/B
  useEffect(() => {
    const loadTests = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Dans une implémentation réelle, nous récupérerions les tests depuis l'API
        // Pour cet exemple, nous utilisons des données fictives
        const mockTests: ABTest[] = [
          {
            id: '123e4567-e89b-12d3-a456-426614174000',
            name: 'Test de niveau de détail des explications',
            description: 'Comparer les explications standard et détaillées pour évaluer leur impact sur la confiance des utilisateurs',
            startDate: '2023-06-01T00:00:00.000Z',
            endDate: '2023-06-30T00:00:00.000Z',
            status: 'COMPLETED',
            variants: 2,
            participants: 1000,
            targetAudience: 'Tous les utilisateurs',
            createdBy: 'Admin',
          },
          {
            id: '123e4567-e89b-12d3-a456-426614174001',
            name: 'Test de présentation visuelle des explications',
            description: 'Comparer les explications textuelles et visuelles pour évaluer leur impact sur la compréhension',
            startDate: '2023-07-01T00:00:00.000Z',
            endDate: null,
            status: 'RUNNING',
            variants: 3,
            participants: 750,
            targetAudience: 'Nouveaux utilisateurs',
            createdBy: 'Admin',
          },
          {
            id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test de personnalisation des explications',
            description: 'Comparer les explications génériques et personnalisées pour évaluer leur impact sur l\'engagement',
            startDate: '2023-08-01T00:00:00.000Z',
            endDate: null,
            status: 'DRAFT',
            variants: 2,
            participants: 0,
            targetAudience: 'Utilisateurs actifs',
            createdBy: 'Admin',
          },
        ];
        
        setTests(mockTests);
      } catch (err: any) {
        console.error('Erreur lors du chargement des tests A/B:', err);
        setError(err.message || 'Erreur lors du chargement des tests A/B');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadTests();
    }
  }, [user]);
  
  // Gérer la création d'un nouveau test
  const handleCreateTest = () => {
    // Valider les champs
    if (!newTest.name.trim()) {
      toast.error('Le nom du test est requis');
      return;
    }
    
    // Dans une implémentation réelle, nous enverrions les données à l'API
    const mockNewTest: ABTest = {
      id: `test-${Date.now()}`,
      name: newTest.name,
      description: newTest.description,
      startDate: new Date().toISOString(),
      endDate: null,
      status: 'DRAFT',
      variants: 0,
      participants: 0,
      targetAudience: newTest.targetAudience === 'all' ? 'Tous les utilisateurs' : 
                      newTest.targetAudience === 'new' ? 'Nouveaux utilisateurs' : 'Utilisateurs actifs',
      createdBy: user?.firstName || 'Admin',
    };
    
    setTests([...tests, mockNewTest]);
    setShowCreateModal(false);
    setNewTest({
      name: '',
      description: '',
      targetAudience: 'all',
    });
    
    toast.success('Test A/B créé avec succès');
  };
  
  // Gérer le changement de statut d'un test
  const handleStatusChange = (testId: string, newStatus: 'DRAFT' | 'RUNNING' | 'PAUSED' | 'COMPLETED') => {
    // Dans une implémentation réelle, nous enverrions la mise à jour à l'API
    const updatedTests = tests.map(test => {
      if (test.id === testId) {
        return {
          ...test,
          status: newStatus,
          endDate: newStatus === 'COMPLETED' ? new Date().toISOString() : test.endDate,
        };
      }
      return test;
    });
    
    setTests(updatedTests);
    toast.success(`Statut du test mis à jour: ${newStatus}`);
  };
  
  // Formater une date
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'En cours';
    
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };
  
  // Obtenir la couleur du statut
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'RUNNING':
        return 'bg-green-100 text-green-800';
      case 'PAUSED':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Tests A/B | Retreat And Be</title>
          <meta
            name="description"
            content="Gestion des tests A/B pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Tests A/B | Retreat And Be</title>
        <meta
          name="description"
          content="Gestion des tests A/B pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tests A/B</h1>
              <p className="text-gray-600">Gérer les tests A/B pour les explications de recommandations</p>
            </div>
            
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
            >
              Créer un test
            </button>
          </div>
          
          {/* Afficher un message d'erreur */}
          {error && (
            <div className="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p>{error}</p>
              </div>
            </div>
          )}
          
          {/* Liste des tests */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nom
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Période
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Variantes
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Participants
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tests.map((test) => (
                    <tr key={test.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <div className="text-sm font-medium text-gray-900">{test.name}</div>
                          <div className="text-sm text-gray-500">{test.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(test.startDate)} - {formatDate(test.endDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(test.status)}`}>
                          {test.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {test.variants}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {test.participants}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => navigate(`/admin/ab-testing/${test.id}/results`)}
                            className="text-retreat-green hover:text-retreat-green-dark"
                          >
                            Résultats
                          </button>
                          {test.status === 'DRAFT' && (
                            <button
                              onClick={() => handleStatusChange(test.id, 'RUNNING')}
                              className="text-green-600 hover:text-green-900"
                            >
                              Démarrer
                            </button>
                          )}
                          {test.status === 'RUNNING' && (
                            <button
                              onClick={() => handleStatusChange(test.id, 'PAUSED')}
                              className="text-yellow-600 hover:text-yellow-900"
                            >
                              Pause
                            </button>
                          )}
                          {test.status === 'PAUSED' && (
                            <button
                              onClick={() => handleStatusChange(test.id, 'RUNNING')}
                              className="text-green-600 hover:text-green-900"
                            >
                              Reprendre
                            </button>
                          )}
                          {(test.status === 'RUNNING' || test.status === 'PAUSED') && (
                            <button
                              onClick={() => handleStatusChange(test.id, 'COMPLETED')}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Terminer
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
      
      {/* Modal de création de test */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Créer un nouveau test A/B</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Nom du test
                </label>
                <input
                  type="text"
                  id="name"
                  value={newTest.name}
                  onChange={(e) => setNewTest({ ...newTest, name: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  placeholder="Ex: Test de niveau de détail des explications"
                />
              </div>
              
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  value={newTest.description}
                  onChange={(e) => setNewTest({ ...newTest, description: e.target.value })}
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  placeholder="Décrivez l'objectif du test..."
                />
              </div>
              
              <div>
                <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700">
                  Public cible
                </label>
                <select
                  id="targetAudience"
                  value={newTest.targetAudience}
                  onChange={(e) => setNewTest({ ...newTest, targetAudience: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  <option value="all">Tous les utilisateurs</option>
                  <option value="new">Nouveaux utilisateurs</option>
                  <option value="active">Utilisateurs actifs</option>
                </select>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleCreateTest}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark"
              >
                Créer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ABTestingPage;
