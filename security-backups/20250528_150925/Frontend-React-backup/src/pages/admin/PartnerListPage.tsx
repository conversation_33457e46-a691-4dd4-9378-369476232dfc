import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { partnerService, Partner } from '../../services/api/partnerService';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';

// Partner status options
const PARTNER_STATUS_OPTIONS = [
  { value: '', label: 'Tous les statuts' },
  { value: 'PENDING', label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'ACTIVE', label: 'Actif', color: 'bg-green-100 text-green-800' },
  { value: 'SUSPENDED', label: 'Suspendu', color: 'bg-red-100 text-red-800' },
  { value: 'TERMINATED', label: 'Résili<PERSON>', color: 'bg-gray-100 text-gray-800' },
];

// Partner type options
const PARTNER_TYPE_OPTIONS = [
  { value: '', label: 'Tous les types' },
  { value: 'PREMIUM_CERTIFIED', label: 'Premium Certifié' },
  { value: 'CERTIFIED', label: 'Certifié' },
  { value: 'STANDARD', label: 'Standard' },
];

const PartnerListPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [partnersPerPage] = useState<number>(10);

  // Load partners
  useEffect(() => {
    const fetchPartners = async () => {
      try {
        setLoading(true);
        const data = await partnerService.getAllPartners(statusFilter);
        setPartners(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching partners:', err);
        setError('Impossible de récupérer la liste des partenaires');
      } finally {
        setLoading(false);
      }
    };

    fetchPartners();
  }, [statusFilter]);

  // Check if user is admin
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Filter partners
  const filteredPartners = partners.filter(partner => {
    // Apply type filter
    if (typeFilter && partner.type !== typeFilter) {
      return false;
    }
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        partner.companyName.toLowerCase().includes(query) ||
        (partner.user?.email && partner.user.email.toLowerCase().includes(query)) ||
        (partner.user?.firstName && partner.user.firstName.toLowerCase().includes(query)) ||
        (partner.user?.lastName && partner.user.lastName.toLowerCase().includes(query))
      );
    }
    
    return true;
  });

  // Pagination
  const indexOfLastPartner = currentPage * partnersPerPage;
  const indexOfFirstPartner = indexOfLastPartner - partnersPerPage;
  const currentPartners = filteredPartners.slice(indexOfFirstPartner, indexOfLastPartner);
  const totalPages = Math.ceil(filteredPartners.length / partnersPerPage);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Get status badge color
  const getStatusColor = (status: string) => {
    const statusOption = PARTNER_STATUS_OPTIONS.find(option => option.value === status);
    return statusOption?.color || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Liste des Partenaires | Administration</title>
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Gestion des partenaires</h1>
          </div>
          
          {/* Filters */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Recherche
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  placeholder="Nom, email..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Statut
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => {
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {PARTNER_STATUS_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={typeFilter}
                  onChange={(e) => {
                    setTypeFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                >
                  {PARTNER_TYPE_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          {/* Partners List */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {error ? (
              <div className="p-6 text-center">
                <p className="text-red-500">{error}</p>
              </div>
            ) : filteredPartners.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-gray-500">Aucun partenaire trouvé</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Entreprise
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Contact
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Type
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Statut
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Date d'inscription
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {currentPartners.map((partner) => (
                        <tr key={partner.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{partner.companyName}</div>
                            <div className="text-sm text-gray-500">{partner.category}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {partner.user ? (
                              <div>
                                <div className="text-sm text-gray-900">
                                  {partner.user.firstName} {partner.user.lastName}
                                </div>
                                <div className="text-sm text-gray-500">{partner.user.email}</div>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-500">Non disponible</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{partner.type}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(
                                partner.status
                              )}`}
                            >
                              {partner.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(partner.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link
                              to={`/admin/partners/${partner.id}`}
                              className="text-retreat-green hover:text-retreat-green-dark"
                            >
                              Gérer
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Affichage de{' '}
                        <span className="font-medium">{indexOfFirstPartner + 1}</span> à{' '}
                        <span className="font-medium">
                          {Math.min(indexOfLastPartner, filteredPartners.length)}
                        </span>{' '}
                        sur <span className="font-medium">{filteredPartners.length}</span> partenaires
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                          onClick={() => paginate(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === 1
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          Précédent
                        </button>
                        {Array.from({ length: totalPages }).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => paginate(index + 1)}
                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                              currentPage === index + 1
                                ? 'z-10 bg-retreat-green text-white border-retreat-green'
                                : 'text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {index + 1}
                          </button>
                        ))}
                        <button
                          onClick={() => paginate(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === totalPages
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          Suivant
                        </button>
                      </nav>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default PartnerListPage;
