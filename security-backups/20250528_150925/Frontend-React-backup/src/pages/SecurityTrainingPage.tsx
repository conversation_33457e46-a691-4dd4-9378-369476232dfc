import React from 'react';
import { Helmet } from 'react-helmet-async';
import { SecurityTraining } from '../components/security';
import MainLayout from '../components/templates/MainLayout/MainLayout';
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const SecurityTrainingPage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  // Vérifier si l'utilisateur est authentifié
  if (!isAuthenticated) {
    return <Navigate to='/login' replace />;
  }

  return (
    <MainLayout>
      <Helmet>
        <title>Formation à la sécurité | Retreat and Be</title>
        <meta
          name='description'
          content='Formations et simulations de sécurité pour améliorer vos connaissances en matière de cybersécurité.'
        />
      </Helmet>

      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-2xl font-bold text-gray-900 mb-6'>Formation à la sécurité</h1>
        <p className='text-gray-600 mb-8'>
          Améliorez vos connaissances en matière de cybersécurité grâce à nos formations
          interactives. Complétez les simulations ci-dessous pour apprendre à vous protéger contre
          les menaces en ligne.
        </p>

        <SecurityTraining />
      </div>
    </MainLayout>
  );
};

export default SecurityTrainingPage;
