#!/bin/bash

# Script de déploiement multi-région pour Front-Audrey-V1-Main-main
# Ce script permet de déployer l'application dans plusieurs régions

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si Docker est installé
  if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si AWS CLI est installé (pour la gestion multi-région)
  if ! command -v aws &> /dev/null; then
    warn "AWS CLI n'est pas installé. Certaines fonctionnalités multi-région pourraient ne pas fonctionner correctement."
  fi
  
  success "Tous les prérequis sont installés."
}

# Afficher l'aide
show_help() {
  echo "Usage: $0 [options] <regions>"
  echo
  echo "Options:"
  echo "  -h, --help                Afficher cette aide"
  echo "  -t, --tag <tag>           Spécifier le tag de l'image (par défaut: latest)"
  echo "  -n, --namespace <ns>      Spécifier le namespace Kubernetes (par défaut: retreat-and-be)"
  echo "  -s, --skip-build          Ignorer la construction de l'image Docker"
  echo "  -p, --skip-push           Ignorer l'envoi de l'image vers le registre"
  echo "  -d, --dry-run             Exécuter en mode simulation (sans déploiement réel)"
  echo "  -a, --all                 Déployer dans toutes les régions"
  echo
  echo "Régions disponibles:"
  echo "  eu                        Europe (eu-west-1)"
  echo "  us                        Amérique du Nord (us-east-1)"
  echo "  ap                        Asie-Pacifique (ap-southeast-1)"
  echo
  echo "Exemples:"
  echo "  $0 eu us                  Déployer en Europe et en Amérique du Nord"
  echo "  $0 --tag v1.0.0 --all     Déployer la version v1.0.0 dans toutes les régions"
  echo "  $0 --skip-build ap        Déployer en Asie-Pacifique sans reconstruire l'image"
}

# Construire l'image Docker pour une région
build_docker_image() {
  local region=$1
  log "Construction de l'image Docker pour la région ${region}..."
  
  # Déterminer le registre Docker en fonction de la région
  local registry
  case ${region} in
    eu)
      registry="registry.eu-west-1.retreat-and-be.com"
      ;;
    us)
      registry="registry.us-east-1.retreat-and-be.com"
      ;;
    ap)
      registry="registry.ap-southeast-1.retreat-and-be.com"
      ;;
  esac
  
  # Construire l'image Docker
  docker build -t ${registry}/retreat-and-be/audrey-frontend:${IMAGE_TAG} .
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la construction de l'image Docker pour la région ${region}."
    return 1
  fi
  
  success "Image Docker construite avec succès pour la région ${region}."
  return 0
}

# Pousser l'image vers le registre
push_docker_image() {
  local region=$1
  log "Envoi de l'image vers le registre Docker pour la région ${region}..."
  
  # Déterminer le registre Docker en fonction de la région
  local registry
  case ${region} in
    eu)
      registry="registry.eu-west-1.retreat-and-be.com"
      ;;
    us)
      registry="registry.us-east-1.retreat-and-be.com"
      ;;
    ap)
      registry="registry.ap-southeast-1.retreat-and-be.com"
      ;;
  esac
  
  # Se connecter au registre Docker si nécessaire
  if [ -z "$DOCKER_LOGGED_IN_${region}" ]; then
    echo "Connexion au registre Docker pour la région ${region}..."
    read -p "Nom d'utilisateur pour le registre Docker (${registry}): " DOCKER_USERNAME
    read -sp "Mot de passe pour le registre Docker (${registry}): " DOCKER_PASSWORD
    echo
    
    echo "$DOCKER_PASSWORD" | docker login ${registry} -u ${DOCKER_USERNAME} --password-stdin
    
    if [ $? -ne 0 ]; then
      error "Erreur lors de la connexion au registre Docker pour la région ${region}."
      return 1
    fi
    
    eval "DOCKER_LOGGED_IN_${region}=true"
  fi
  
  # Pousser l'image
  docker push ${registry}/retreat-and-be/audrey-frontend:${IMAGE_TAG}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'envoi de l'image vers le registre Docker pour la région ${region}."
    return 1
  fi
  
  success "Image Docker envoyée avec succès pour la région ${region}."
  return 0
}

# Déployer avec Helm
deploy_with_helm() {
  local region=$1
  log "Déploiement dans la région ${region} avec Helm..."
  
  # Changer de contexte Kubernetes en fonction de la région
  log "Changement de contexte Kubernetes pour la région ${region}..."
  
  # Déterminer le contexte Kubernetes en fonction de la région
  local context
  case ${region} in
    eu)
      context="retreat-and-be-eu-west-1"
      ;;
    us)
      context="retreat-and-be-us-east-1"
      ;;
    ap)
      context="retreat-and-be-ap-southeast-1"
      ;;
  esac
  
  # Changer de contexte Kubernetes
  kubectl config use-context ${context}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors du changement de contexte Kubernetes pour la région ${region}."
    return 1
  fi
  
  # Vérifier si le namespace existe, sinon le créer
  if ! kubectl get namespace ${NAMESPACE} &> /dev/null; then
    log "Création du namespace ${NAMESPACE}..."
    kubectl create namespace ${NAMESPACE}
  fi
  
  # Préparer la commande Helm
  HELM_CMD="helm upgrade --install audrey-frontend-${region} ./helm -f helm/multi-region/values-${region}.yaml -n ${NAMESPACE} --set image.tag=${IMAGE_TAG}"
  
  # Ajouter l'option dry-run si nécessaire
  if [ "$DRY_RUN" = true ]; then
    HELM_CMD="${HELM_CMD} --dry-run"
  fi
  
  # Exécuter la commande Helm
  log "Exécution de la commande: ${HELM_CMD}"
  eval ${HELM_CMD}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors du déploiement avec Helm pour la région ${region}."
    return 1
  fi
  
  success "Déploiement avec Helm réussi pour la région ${region}."
  return 0
}

# Vérifier le déploiement
verify_deployment() {
  local region=$1
  
  if [ "$DRY_RUN" = true ]; then
    log "Mode simulation: vérification du déploiement ignorée pour la région ${region}."
    return 0
  fi
  
  log "Vérification du déploiement pour la région ${region}..."
  
  # Déterminer le contexte Kubernetes en fonction de la région
  local context
  case ${region} in
    eu)
      context="retreat-and-be-eu-west-1"
      ;;
    us)
      context="retreat-and-be-us-east-1"
      ;;
    ap)
      context="retreat-and-be-ap-southeast-1"
      ;;
  esac
  
  # Changer de contexte Kubernetes
  kubectl config use-context ${context}
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend-${region} -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la vérification du déploiement pour la région ${region}."
    return 1
  fi
  
  # Afficher les informations du service
  kubectl get service audrey-frontend-${region} -n ${NAMESPACE}
  
  # Afficher les informations de l'ingress
  kubectl get ingress -n ${NAMESPACE} | grep audrey-frontend-${region}
  
  success "Déploiement vérifié avec succès pour la région ${region}."
  
  # Afficher l'URL d'accès
  case ${region} in
    eu)
      log "L'application est accessible à l'adresse: https://eu.app.retreat-and-be.com"
      ;;
    us)
      log "L'application est accessible à l'adresse: https://us.app.retreat-and-be.com"
      ;;
    ap)
      log "L'application est accessible à l'adresse: https://ap.app.retreat-and-be.com"
      ;;
  esac
  
  return 0
}

# Configurer Route 53 pour le routage global
configure_route53() {
  if [ "$DRY_RUN" = true ]; then
    log "Mode simulation: configuration de Route 53 ignorée."
    return 0
  fi
  
  log "Configuration de Route 53 pour le routage global..."
  
  # Vérifier si AWS CLI est installé
  if ! command -v aws &> /dev/null; then
    warn "AWS CLI n'est pas installé. Configuration de Route 53 ignorée."
    return 1
  fi
  
  # Vérifier si le profil AWS est configuré
  if ! aws configure list &> /dev/null; then
    warn "Profil AWS non configuré. Configuration de Route 53 ignorée."
    return 1
  fi
  
  # Mettre à jour la politique de routage Route 53
  log "Mise à jour de la politique de routage Route 53..."
  
  # Exemple de commande AWS CLI pour mettre à jour la politique de routage
  # Cette commande doit être adaptée en fonction de votre configuration Route 53
  aws route53 change-resource-record-sets --hosted-zone-id ZXXXXXXXXXXXXX --change-batch '{
    "Changes": [
      {
        "Action": "UPSERT",
        "ResourceRecordSet": {
          "Name": "app.retreat-and-be.com",
          "Type": "A",
          "SetIdentifier": "global",
          "MultiValueAnswer": false,
          "TTL": 60,
          "ResourceRecords": [
            {
              "Value": "REPLACE_WITH_ACTUAL_IP"
            }
          ],
          "HealthCheckId": "REPLACE_WITH_HEALTH_CHECK_ID",
          "Region": "global"
        }
      }
    ]
  }'
  
  if [ $? -ne 0 ]; then
    warn "Erreur lors de la mise à jour de la politique de routage Route 53."
    return 1
  fi
  
  success "Configuration de Route 53 terminée avec succès."
  return 0
}

# Variables par défaut
NAMESPACE="retreat-and-be"
IMAGE_TAG="latest"
SKIP_BUILD=false
SKIP_PUSH=false
DRY_RUN=false
DEPLOY_ALL=false
REGIONS=()

# Analyser les arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -t|--tag)
      IMAGE_TAG="$2"
      shift
      shift
      ;;
    -n|--namespace)
      NAMESPACE="$2"
      shift
      shift
      ;;
    -s|--skip-build)
      SKIP_BUILD=true
      shift
      ;;
    -p|--skip-push)
      SKIP_PUSH=true
      shift
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -a|--all)
      DEPLOY_ALL=true
      shift
      ;;
    eu|us|ap)
      REGIONS+=("$1")
      shift
      ;;
    *)
      error "Option inconnue: $1"
      show_help
      exit 1
      ;;
  esac
done

# Si --all est spécifié, déployer dans toutes les régions
if [ "$DEPLOY_ALL" = true ]; then
  REGIONS=("eu" "us" "ap")
fi

# Vérifier si au moins une région est spécifiée
if [ ${#REGIONS[@]} -eq 0 ]; then
  error "Aucune région spécifiée."
  show_help
  exit 1
fi

# Menu principal
main() {
  echo "================================================"
  echo "  Déploiement multi-région de Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Afficher la configuration
  echo
  echo "Configuration:"
  echo "- Régions: ${REGIONS[*]}"
  echo "- Namespace Kubernetes: ${NAMESPACE}"
  echo "- Tag de l'image: ${IMAGE_TAG}"
  echo "- Ignorer la construction: ${SKIP_BUILD}"
  echo "- Ignorer l'envoi: ${SKIP_PUSH}"
  echo "- Mode simulation: ${DRY_RUN}"
  echo
  
  # Demander confirmation
  read -p "Voulez-vous continuer avec cette configuration? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Déploiement annulé."
    exit 0
  fi
  
  # Déployer dans chaque région
  for region in "${REGIONS[@]}"; do
    echo
    echo "================================================"
    echo "  Déploiement dans la région: ${region}"
    echo "================================================"
    
    # Construire l'image Docker si nécessaire
    if [ "$SKIP_BUILD" = false ]; then
      if ! build_docker_image ${region}; then
        warn "Échec de la construction de l'image Docker pour la région ${region}. Passage à la région suivante."
        continue
      fi
    else
      log "Construction de l'image Docker ignorée pour la région ${region}."
    fi
    
    # Pousser l'image vers le registre si nécessaire
    if [ "$SKIP_PUSH" = false ]; then
      if ! push_docker_image ${region}; then
        warn "Échec de l'envoi de l'image vers le registre Docker pour la région ${region}. Passage à la région suivante."
        continue
      fi
    else
      log "Envoi de l'image vers le registre Docker ignoré pour la région ${region}."
    fi
    
    # Déployer avec Helm
    if ! deploy_with_helm ${region}; then
      warn "Échec du déploiement avec Helm pour la région ${region}. Passage à la région suivante."
      continue
    fi
    
    # Vérifier le déploiement
    if ! verify_deployment ${region}; then
      warn "Échec de la vérification du déploiement pour la région ${region}. Passage à la région suivante."
      continue
    fi
    
    success "Déploiement dans la région ${region} terminé avec succès!"
  done
  
  # Configurer Route 53 pour le routage global
  configure_route53
  
  success "Déploiement multi-région terminé avec succès!"
}

# Exécuter le script
main
