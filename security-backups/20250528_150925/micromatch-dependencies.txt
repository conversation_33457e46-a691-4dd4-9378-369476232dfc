agentic-coding-framework-rb2@3.8.1 /Users/<USER>/Desktop/Agentic-Coding-Framework- RB2
└─┬ retreat-and-be-backend@0.1.0 -> ./Projet-RB2/Backend-NestJS
  ├─┬ @types/jest@29.5.14
  │ └─┬ expect@29.7.0
  │   └─┬ jest-message-util@29.7.0
  │     └── micromatch@4.0.5 deduped
  ├─┬ @typescript-eslint/parser@6.21.0
  │ └─┬ @typescript-eslint/typescript-estree@6.21.0
  │   └─┬ globby@11.1.0
  │     └─┬ fast-glob@3.3.3
  │       └── micromatch@4.0.8
  ├─┬ jest@29.7.0
  │ └─┬ @jest/core@29.7.0
  │   ├─┬ jest-config@29.7.0
  │   │ └── micromatch@4.0.5 deduped
  │   ├─┬ jest-haste-map@29.7.0
  │   │ └── micromatch@4.0.5 deduped
  │   └── micromatch@4.0.5 deduped
  ├─┬ ts-jest@29.3.4
  │ └─┬ @jest/transform@29.7.0
  │   └── micromatch@4.0.5 deduped
  └─┬ ts-loader@9.5.2
    └── micromatch@4.0.5

