{"auditReportVersion": 2, "vulnerabilities": {"@svgr/plugin-svgo": {"name": "@svgr/plugin-svgo", "severity": "high", "isDirect": false, "via": ["svgo"], "effects": ["@svgr/webpack"], "range": "<=5.5.0", "nodes": ["node_modules/@svgr/plugin-svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "@svgr/webpack": {"name": "@svgr/webpack", "severity": "high", "isDirect": false, "via": ["@svgr/plugin-svgo"], "effects": ["react-scripts"], "range": "4.0.0 - 5.5.0", "nodes": ["node_modules/@svgr/webpack"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "css-select": {"name": "css-select", "severity": "high", "isDirect": false, "via": ["nth-check"], "effects": ["svgo"], "range": "<=3.1.0", "nodes": ["node_modules/svgo/node_modules/css-select"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "esbuild": {"name": "esbuild", "severity": "moderate", "isDirect": false, "via": [{"source": 1102341, "name": "esbuild", "dependency": "esbuild", "title": "esbuild enables any website to send any requests to the development server and read the response", "url": "https://github.com/advisories/GHSA-67mh-4wv8-2f99", "severity": "moderate", "cwe": ["CWE-346"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": "<=0.24.2"}], "effects": ["vite"], "range": "<=0.24.2", "nodes": ["node_modules/vite/node_modules/esbuild"], "fixAvailable": {"name": "vite", "version": "6.3.5", "isSemVerMajor": true}}, "micromatch": {"name": "micromatch", "severity": "moderate", "isDirect": false, "via": [{"source": 1098681, "name": "micromatch", "dependency": "micromatch", "title": "Regular Expression Denial of Service (ReDoS) in micromatch", "url": "https://github.com/advisories/GHSA-952p-6rrq-rcjv", "severity": "moderate", "cwe": ["CWE-1333"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L"}, "range": "<4.0.8"}], "effects": [], "range": "<4.0.8", "nodes": ["node_modules/micromatch"], "fixAvailable": true}, "nth-check": {"name": "nth-check", "severity": "high", "isDirect": false, "via": [{"source": 1095141, "name": "nth-check", "dependency": "nth-check", "title": "Inefficient Regular Expression Complexity in nth-check", "url": "https://github.com/advisories/GHSA-rp65-9cf3-cjxr", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<2.0.1"}], "effects": ["css-select"], "range": "<2.0.1", "nodes": ["node_modules/svgo/node_modules/nth-check"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "postcss": {"name": "postcss", "severity": "moderate", "isDirect": false, "via": [{"source": 1094544, "name": "postcss", "dependency": "postcss", "title": "PostCSS line return parsing error", "url": "https://github.com/advisories/GHSA-7fh5-64p2-3v2j", "severity": "moderate", "cwe": ["CWE-74", "CWE-144"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N"}, "range": "<8.4.31"}], "effects": ["resolve-url-loader"], "range": "<8.4.31", "nodes": ["node_modules/react-scripts/node_modules/resolve-url-loader/node_modules/postcss"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "react-scripts": {"name": "react-scripts", "severity": "high", "isDirect": true, "via": ["@svgr/webpack", "resolve-url-loader"], "effects": [], "range": ">=2.1.4", "nodes": ["node_modules/react-scripts"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "resolve-url-loader": {"name": "resolve-url-loader", "severity": "moderate", "isDirect": false, "via": ["postcss"], "effects": ["react-scripts"], "range": "0.0.1-experiment-postcss || 3.0.0-alpha.1 - 4.0.0", "nodes": ["node_modules/react-scripts/node_modules/resolve-url-loader"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "svgo": {"name": "svgo", "severity": "high", "isDirect": false, "via": ["css-select"], "effects": ["@svgr/plugin-svgo"], "range": "1.0.0 - 1.3.2", "nodes": ["node_modules/svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "vite": {"name": "vite", "severity": "moderate", "isDirect": true, "via": ["esbuild"], "effects": [], "range": "0.11.0 - 6.1.6", "nodes": ["node_modules/vite"], "fixAvailable": {"name": "vite", "version": "6.3.5", "isSemVerMajor": true}}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 5, "high": 6, "critical": 0, "total": 11}, "dependencies": {"prod": 2819, "dev": 1167, "optional": 259, "peer": 4, "peerOptional": 0, "total": 4106}}}