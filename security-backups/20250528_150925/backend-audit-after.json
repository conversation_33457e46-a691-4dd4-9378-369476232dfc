{"auditReportVersion": 2, "vulnerabilities": {"micromatch": {"name": "micromatch", "severity": "moderate", "isDirect": false, "via": [{"source": 1098681, "name": "micromatch", "dependency": "micromatch", "title": "Regular Expression Denial of Service (ReDoS) in micromatch", "url": "https://github.com/advisories/GHSA-952p-6rrq-rcjv", "severity": "moderate", "cwe": ["CWE-1333"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L"}, "range": "<4.0.8"}], "effects": [], "range": "<4.0.8", "nodes": ["node_modules/micromatch"], "fixAvailable": true}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 1, "high": 0, "critical": 0, "total": 1}, "dependencies": {"prod": 2819, "dev": 1167, "optional": 259, "peer": 4, "peerOptional": 0, "total": 4106}}}