{"name": "retreat-and-be", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "cypress open", "test:e2e:headless": "cypress run", "test:e2e:chrome": "cypress run --browser chrome", "test:e2e:firefox": "cypress run --browser firefox", "test:component": "cypress run --component", "test:component:open": "cypress open --component", "test:a11y": "cypress run --spec 'cypress/e2e/accessibility/**/*'", "test:performance": "k6 run tests/performance/load-test.js", "test:performance:advanced": "node scripts/advanced-performance-test.js", "test:all": "npm run test:coverage && npm run test:e2e:headless && npm run test:component", "quality:report": "tsx scripts/quality-report.ts", "quality:check": "npm run lint && npm run type-check && npm run test:coverage", "optimize:assets": "node scripts/optimize-assets.ts", "cdn:setup": "bash scripts/setup-optimized-cdn.sh", "build:optimized": "npm run optimize:assets && vite build --config vite.config.optimized.ts", "analyze:bundle": "npm run build && npx vite-bundle-analyzer dist", "performance:audit": "npm run test:performance:advanced && npm run analyze:bundle", "validate:phase3-sprint1": "node scripts/validate-phase3-sprint1.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "echo 'Skipping husky install for security audit'", "migrate:final": "tsx scripts/finalize-migration.ts", "launch:check": "tsx scripts/launch-readiness-check.ts", "deploy:staging": "tsx scripts/deploy-production.ts staging", "deploy:production": "tsx scripts/deploy-production.ts production", "monitor:start": "tsx scripts/production-monitoring.ts", "monitor:report": "tsx scripts/production-monitoring.ts report", "launch:commercial": "tsx scripts/commercial-launch.ts", "audit:migration": "tsx scripts/migration-audit.ts"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.69.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react-datepicker": "^6.2.0", "@types/react-helmet-async": "^1.0.1", "@types/uuid": "^9.0.8", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "axios": "^1.6.8", "chart.js": "^4.4.9", "clsx": "^2.1.1", "framer-motion": "^12.11.4", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "mapbox-gl": "^3.11.0", "next": "^15.3.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-map-gl": "^8.0.4", "react-range": "^1.10.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "styled-components": "^6.1.16", "tailwind-merge": "^3.3.0", "typescript": "^4.9.5", "uuid": "^9.0.1", "zod": "^3.24.4", "zustand": "^5.0.4", "@retreat-and-be/design-system": "file:../design-system", "class-variance-authority": "^0.7.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.13", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-storybook": "^0.12.0", "husky": "^9.1.7", "lighthouse": "^12.6.0", "chrome-launcher": "^1.1.2", "sharp": "^0.33.5", "glob": "^11.0.0", "rollup-plugin-visualizer": "^5.12.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-bundle-analyzer": "^0.11.0", "cssnano": "^7.0.6", "lint-staged": "^16.0.0", "postcss": "^8.4.38", "prettier": "^3.5.3", "storybook": "^8.6.14", "tailwindcss": "^3.2.7", "web-vitals": "^5.0.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": "prettier --write"}, "resolutions": {"postcss": "^8.4.38", "nth-check": "^2.1.1", "lodash": "^4.17.21", "axios": "^1.6.8", "semver": "^7.5.4", "json5": "^2.2.3", "minimist": "^1.2.8", "qs": "^6.11.2"}, "eslintConfig": {"extends": ["react-app"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "jsx-a11y/anchor-is-valid": "warn", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/no-static-element-interactions": "warn", "jsx-a11y/label-has-associated-control": "warn", "react/no-unescaped-entities": "warn"}}}