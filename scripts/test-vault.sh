#!/bin/bash

# 🧪 TEST DE VAULT
# Utilisation: ./scripts/test-vault.sh

export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"

echo "🧪 Test de Vault..."

# Test de connexion
if vault status > /dev/null 2>&1; then
    echo "✅ Connexion à Vault: OK"
else
    echo "❌ Connexion à Vault: ÉCHEC"
    exit 1
fi

# Test de lecture des secrets
echo "📋 Secrets disponibles:"
vault kv list secret/ 2>/dev/null | grep -v "^Keys" | grep -v "^----" | while read path; do
    if [[ -n "$path" ]]; then
        echo "  - secret/$path"
    fi
done

# Test de récupération d'un secret
echo ""
echo "🔑 Test de récupération du secret JWT:"
JWT_SECRET=$(vault kv get -field=jwt_secret secret/auth 2>/dev/null)
if [[ -n "$JWT_SECRET" ]]; then
    echo "✅ JWT Secret récupéré: ${JWT_SECRET:0:10}..."
else
    echo "❌ Impossible de récupérer le JWT Secret"
fi

echo ""
echo "🎯 Test terminé"
