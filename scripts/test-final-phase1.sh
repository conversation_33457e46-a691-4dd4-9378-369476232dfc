#!/bin/bash

# 🧪 TEST FINAL DE VALIDATION - PHASE 1 SÉCURITÉ
# Date: 28 mai 2025
# Objectif: Validation complète de bout en bout

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variables
PROJECT_ROOT="/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2"
TEST_RESULTS="/tmp/phase1-final-test-results.txt"

echo "🧪 TEST FINAL DE VALIDATION - PHASE 1 SÉCURITÉ"
echo "=============================================="

# Initialiser le rapport de test
cat > "$TEST_RESULTS" << EOF
# 🧪 RÉSULTATS TEST FINAL - PHASE 1 SÉCURITÉ
Date: $(date)
Testeur: Script automatisé
Durée: En cours...

## Tests Effectués

EOF

TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Fonction pour enregistrer un test
record_test() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$status" == "PASS" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        echo "✅ **$test_name**: PASSÉ - $details" >> "$TEST_RESULTS"
        success "$test_name: PASSÉ"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "❌ **$test_name**: ÉCHEC - $details" >> "$TEST_RESULTS"
        error "$test_name: ÉCHEC"
    fi
}

cd "$PROJECT_ROOT"

# TEST 1: Vault Status
log "Test 1: Statut de HashiCorp Vault"
if curl -s http://localhost:8200/v1/sys/health > /dev/null 2>&1; then
    record_test "Vault Status" "PASS" "Vault accessible sur port 8200"
else
    record_test "Vault Status" "FAIL" "Vault non accessible"
fi

# TEST 2: Vault Authentication
log "Test 2: Authentification Vault"
export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"
if vault status > /dev/null 2>&1; then
    record_test "Vault Authentication" "PASS" "Authentification réussie avec token dev"
else
    record_test "Vault Authentication" "FAIL" "Échec authentification"
fi

# TEST 3: Secrets Availability
log "Test 3: Disponibilité des secrets"
SECRET_COUNT=$(vault kv list secret/ 2>/dev/null | grep -v "^Keys" | grep -v "^----" | wc -l)
if [[ $SECRET_COUNT -ge 5 ]]; then
    record_test "Secrets Availability" "PASS" "$SECRET_COUNT groupes de secrets disponibles"
else
    record_test "Secrets Availability" "FAIL" "Seulement $SECRET_COUNT groupes trouvés"
fi

# TEST 4: Secret Retrieval
log "Test 4: Récupération des secrets"
JWT_SECRET=$(vault kv get -field=jwt_secret secret/auth 2>/dev/null)
if [[ -n "$JWT_SECRET" && ${#JWT_SECRET} -gt 10 ]]; then
    record_test "Secret Retrieval" "PASS" "JWT Secret récupéré (${#JWT_SECRET} caractères)"
else
    record_test "Secret Retrieval" "FAIL" "Impossible de récupérer JWT Secret"
fi

# TEST 5: Load Secrets Script
log "Test 5: Script de chargement des secrets"
if source ./scripts/load-vault-secrets.sh > /dev/null 2>&1; then
    if [[ -n "$DATABASE_URL" && -n "$JWT_SECRET" ]]; then
        record_test "Load Secrets Script" "PASS" "Variables d'environnement chargées"
    else
        record_test "Load Secrets Script" "FAIL" "Variables non chargées"
    fi
else
    record_test "Load Secrets Script" "FAIL" "Échec exécution script"
fi

# TEST 6: WAF Configuration Files
log "Test 6: Fichiers de configuration WAF"
if [[ -f "waf-config/nginx-waf.conf" && -f "waf-config/modsec-main.conf" ]]; then
    NGINX_LINES=$(wc -l < waf-config/nginx-waf.conf)
    MODSEC_LINES=$(wc -l < waf-config/modsec-main.conf)
    if [[ $NGINX_LINES -gt 100 && $MODSEC_LINES -gt 30 ]]; then
        record_test "WAF Configuration Files" "PASS" "Nginx: $NGINX_LINES lignes, ModSec: $MODSEC_LINES lignes"
    else
        record_test "WAF Configuration Files" "FAIL" "Fichiers incomplets"
    fi
else
    record_test "WAF Configuration Files" "FAIL" "Fichiers manquants"
fi

# TEST 7: Docker Compose WAF
log "Test 7: Configuration Docker Compose WAF"
if [[ -f "waf-config/docker-compose-waf.yml" ]]; then
    if grep -q "owasp/modsecurity-nginx" waf-config/docker-compose-waf.yml; then
        record_test "Docker Compose WAF" "PASS" "Image ModSecurity configurée"
    else
        record_test "Docker Compose WAF" "FAIL" "Image ModSecurity manquante"
    fi
else
    record_test "Docker Compose WAF" "FAIL" "Fichier docker-compose-waf.yml manquant"
fi

# TEST 8: Deployment Scripts
log "Test 8: Scripts de déploiement"
SCRIPT_COUNT=0
for script in waf-config/deploy-secure.sh waf-config/security-tests.sh waf-config/sql-injection-audit.sh; do
    if [[ -x "$script" ]]; then
        SCRIPT_COUNT=$((SCRIPT_COUNT + 1))
    fi
done

if [[ $SCRIPT_COUNT -eq 3 ]]; then
    record_test "Deployment Scripts" "PASS" "3 scripts exécutables trouvés"
else
    record_test "Deployment Scripts" "FAIL" "Seulement $SCRIPT_COUNT scripts exécutables"
fi

# TEST 9: Security Backups
log "Test 9: Backups de sécurité"
BACKUP_DIRS=$(find security-backups -type d -name "202505*" | wc -l)
if [[ $BACKUP_DIRS -ge 3 ]]; then
    record_test "Security Backups" "PASS" "$BACKUP_DIRS dossiers de backup trouvés"
else
    record_test "Security Backups" "FAIL" "Backups insuffisants ($BACKUP_DIRS)"
fi

# TEST 10: Documentation
log "Test 10: Documentation complète"
DOC_COUNT=0
for doc in RAPPORT_SECURITE_PHASE1.md SYNTHESE_IMPLEMENTATION_PHASE1.md VALIDATION_PHASE1_SECURITE.md; do
    if [[ -f "$doc" ]]; then
        DOC_COUNT=$((DOC_COUNT + 1))
    fi
done

if [[ $DOC_COUNT -eq 3 ]]; then
    record_test "Documentation" "PASS" "3 documents principaux trouvés"
else
    record_test "Documentation" "FAIL" "Documentation incomplète ($DOC_COUNT/3)"
fi

# TEST 11: Vault Process
log "Test 11: Processus Vault actif"
if ps aux | grep -v grep | grep "vault server" > /dev/null; then
    VAULT_PID=$(ps aux | grep -v grep | grep "vault server" | awk '{print $2}')
    record_test "Vault Process" "PASS" "Processus Vault actif (PID: $VAULT_PID)"
else
    record_test "Vault Process" "FAIL" "Processus Vault non trouvé"
fi

# TEST 12: Environment Variables
log "Test 12: Variables d'environnement"
ENV_COUNT=0
for var in DATABASE_URL JWT_SECRET API_KEY_OPENAI ENCRYPTION_KEY; do
    if [[ -n "${!var}" ]]; then
        ENV_COUNT=$((ENV_COUNT + 1))
    fi
done

if [[ $ENV_COUNT -eq 4 ]]; then
    record_test "Environment Variables" "PASS" "4 variables critiques chargées"
else
    record_test "Environment Variables" "FAIL" "Variables manquantes ($ENV_COUNT/4)"
fi

# Finaliser le rapport
cat >> "$TEST_RESULTS" << EOF

## Résumé des Tests

- **Total des tests**: $TOTAL_TESTS
- **Tests réussis**: $PASSED_TESTS
- **Tests échoués**: $FAILED_TESTS
- **Taux de réussite**: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%

## Statut Global

EOF

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo "✅ **VALIDATION COMPLÈTE**: Tous les tests sont passés avec succès" >> "$TEST_RESULTS"
    GLOBAL_STATUS="SUCCESS"
elif [[ $FAILED_TESTS -le 2 ]]; then
    echo "⚠️ **VALIDATION PARTIELLE**: Tests majoritairement réussis avec quelques avertissements" >> "$TEST_RESULTS"
    GLOBAL_STATUS="WARNING"
else
    echo "❌ **VALIDATION ÉCHOUÉE**: Trop de tests ont échoué" >> "$TEST_RESULTS"
    GLOBAL_STATUS="FAILURE"
fi

cat >> "$TEST_RESULTS" << EOF

## Recommandations

EOF

if [[ $GLOBAL_STATUS == "SUCCESS" ]]; then
    cat >> "$TEST_RESULTS" << EOF
✅ **Phase 1 Sécurité validée avec succès**
- Tous les composants fonctionnent correctement
- Prêt pour le passage au Sprint 14
- Infrastructure de sécurité opérationnelle

EOF
elif [[ $GLOBAL_STATUS == "WARNING" ]]; then
    cat >> "$TEST_RESULTS" << EOF
⚠️ **Phase 1 Sécurité partiellement validée**
- La plupart des composants fonctionnent
- Quelques ajustements mineurs nécessaires
- Validation conditionnelle pour continuer

EOF
else
    cat >> "$TEST_RESULTS" << EOF
❌ **Phase 1 Sécurité nécessite des corrections**
- Plusieurs composants critiques défaillants
- Corrections nécessaires avant continuation
- Révision de l'implémentation recommandée

EOF
fi

cat >> "$TEST_RESULTS" << EOF
---

*Test final généré le $(date)*  
*Script: test-final-phase1.sh*  
*Équipe: Agentic Coding Framework RB2*
EOF

# Affichage du résumé
echo ""
echo "🎯 RÉSUMÉ DU TEST FINAL"
echo "======================"
echo "📊 Total des tests: $TOTAL_TESTS"
echo "✅ Tests réussis: $PASSED_TESTS"
echo "❌ Tests échoués: $FAILED_TESTS"
echo "📈 Taux de réussite: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
echo ""

if [[ $GLOBAL_STATUS == "SUCCESS" ]]; then
    success "🎉 VALIDATION COMPLÈTE - PHASE 1 SÉCURITÉ VALIDÉE AVEC SUCCÈS!"
    echo "✅ Tous les composants fonctionnent parfaitement"
    echo "🚀 Prêt pour le Sprint 14 - Tests E2E"
elif [[ $GLOBAL_STATUS == "WARNING" ]]; then
    warning "⚠️ VALIDATION PARTIELLE - Quelques ajustements mineurs nécessaires"
    echo "🔧 Révision recommandée avant continuation"
else
    error "❌ VALIDATION ÉCHOUÉE - Corrections nécessaires"
    echo "🛠️ Révision de l'implémentation requise"
fi

echo ""
echo "📋 Rapport détaillé: $TEST_RESULTS"
echo "📞 Contact: Agent Sécurité pour questions"
echo ""

# Retourner le code de sortie approprié
if [[ $GLOBAL_STATUS == "SUCCESS" ]]; then
    exit 0
elif [[ $GLOBAL_STATUS == "WARNING" ]]; then
    exit 1
else
    exit 2
fi
EOF
