#!/bin/bash

# 🔐 CONFIGURATION SIMPLE DE VAULT - PHASE 1 SÉCURITÉ
# Date: 28 mai 2025
# Objectif: Configuration rapide de Vault pour la migration des secrets

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variables
PROJECT_ROOT="/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2"
VAULT_DIR="$PROJECT_ROOT/vault-config"
SECRETS_BACKUP_DIR="$PROJECT_ROOT/security-backups/$(date +%Y%m%d_%H%M%S)-vault-simple"

echo "🔐 CONFIGURATION SIMPLE DE VAULT"
echo "================================"

# Créer les dossiers nécessaires
mkdir -p "$VAULT_DIR"
mkdir -p "$SECRETS_BACKUP_DIR"

# Vérifier si Vault est installé
if ! command -v vault &> /dev/null; then
    error "Vault n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

success "Vault est installé"

# Configuration en mode développement
log "Démarrage de Vault en mode développement..."

# Tuer tout processus Vault existant
pkill vault 2>/dev/null || true
sleep 2

# Démarrer Vault en mode dev (plus simple)
vault server -dev -dev-root-token-id="dev-token" -dev-listen-address="0.0.0.0:8200" > "$VAULT_DIR/vault-dev.log" 2>&1 &
VAULT_PID=$!

# Attendre que Vault soit prêt
sleep 5

# Configuration des variables d'environnement
export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"

log "Test de connexion à Vault..."
if vault status > /dev/null 2>&1; then
    success "Connexion à Vault réussie"
else
    error "Impossible de se connecter à Vault"
    exit 1
fi

# Créer les secrets de base
log "Création des secrets de base..."

# Secrets de base de données
vault kv put secret/database \
    url="postgresql://user:password@localhost:5432/retreat_and_be" \
    mongodb_uri="mongodb://localhost:27017/retreatandbe" \
    redis_url="redis://localhost:6379"

# Secrets JWT
vault kv put secret/auth \
    jwt_secret="$(openssl rand -base64 32)" \
    jwt_refresh_secret="$(openssl rand -base64 32)" \
    session_secret="$(openssl rand -base64 32)"

# Clés API (placeholders)
vault kv put secret/api \
    openai_key="sk-your-openai-key-here" \
    stripe_key="sk_test_your-stripe-key-here" \
    sendgrid_key="SG.your-sendgrid-key-here"

# Secrets de sécurité
vault kv put secret/security \
    encryption_key="$(openssl rand -base64 32)" \
    hash_salt="$(openssl rand -base64 16)"

# Monitoring
vault kv put secret/monitoring \
    sentry_dsn="https://your-sentry-dsn-here"

success "Secrets créés dans Vault"

# Créer le fichier de configuration d'environnement
log "Création du fichier de configuration..."

cat > "$PROJECT_ROOT/.env.vault" << EOF
# 🔐 CONFIGURATION VAULT - MODE DÉVELOPPEMENT
# Généré automatiquement le $(date)

# Configuration Vault
VAULT_ADDR=http://127.0.0.1:8200
VAULT_TOKEN=dev-token

# Note: En production, utilisez des tokens avec des permissions limitées
EOF

# Créer le script d'accès aux secrets
cat > "$PROJECT_ROOT/scripts/load-vault-secrets.sh" << EOF
#!/bin/bash

# 🔐 CHARGEMENT DES SECRETS DEPUIS VAULT
# Utilisation: source ./scripts/load-vault-secrets.sh

export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"

# Fonction pour récupérer un secret
get_secret() {
    local path="\$1"
    local key="\$2"
    vault kv get -field="\$key" "secret/\$path" 2>/dev/null
}

# Charger les secrets dans les variables d'environnement
log() {
    echo -e "\033[0;34m[VAULT]\033[0m \$1"
}

log "Chargement des secrets depuis Vault..."

export DATABASE_URL=\$(get_secret "database" "url")
export MONGODB_URI=\$(get_secret "database" "mongodb_uri")
export REDIS_URL=\$(get_secret "database" "redis_url")

export JWT_SECRET=\$(get_secret "auth" "jwt_secret")
export JWT_REFRESH_SECRET=\$(get_secret "auth" "jwt_refresh_secret")
export SESSION_SECRET=\$(get_secret "auth" "session_secret")

export API_KEY_OPENAI=\$(get_secret "api" "openai_key")
export API_KEY_STRIPE=\$(get_secret "api" "stripe_key")
export API_KEY_SENDGRID=\$(get_secret "api" "sendgrid_key")

export ENCRYPTION_KEY=\$(get_secret "security" "encryption_key")
export HASH_SALT=\$(get_secret "security" "hash_salt")

export SENTRY_DSN=\$(get_secret "monitoring" "sentry_dsn")

log "✅ Secrets chargés avec succès"
log "📊 Variables disponibles: DATABASE_URL, JWT_SECRET, API_KEY_OPENAI, etc."
EOF

chmod +x "$PROJECT_ROOT/scripts/load-vault-secrets.sh"

# Créer un script de test
cat > "$PROJECT_ROOT/scripts/test-vault.sh" << EOF
#!/bin/bash

# 🧪 TEST DE VAULT
# Utilisation: ./scripts/test-vault.sh

export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"

echo "🧪 Test de Vault..."

# Test de connexion
if vault status > /dev/null 2>&1; then
    echo "✅ Connexion à Vault: OK"
else
    echo "❌ Connexion à Vault: ÉCHEC"
    exit 1
fi

# Test de lecture des secrets
echo "📋 Secrets disponibles:"
vault kv list secret/ 2>/dev/null | grep -v "^Keys" | grep -v "^----" | while read path; do
    if [[ -n "\$path" ]]; then
        echo "  - secret/\$path"
    fi
done

# Test de récupération d'un secret
echo ""
echo "🔑 Test de récupération du secret JWT:"
JWT_SECRET=\$(vault kv get -field=jwt_secret secret/auth 2>/dev/null)
if [[ -n "\$JWT_SECRET" ]]; then
    echo "✅ JWT Secret récupéré: \${JWT_SECRET:0:10}..."
else
    echo "❌ Impossible de récupérer le JWT Secret"
fi

echo ""
echo "🎯 Test terminé"
EOF

chmod +x "$PROJECT_ROOT/scripts/test-vault.sh"

# Créer le rapport de migration
cat > "$SECRETS_BACKUP_DIR/vault-setup-report.md" << EOF
# 🔐 RAPPORT DE CONFIGURATION VAULT

## Informations
- **Date**: $(date)
- **Mode**: Développement
- **Vault Address**: http://127.0.0.1:8200
- **Token**: dev-token (développement uniquement)

## Secrets Créés

### Base de données (secret/database)
- url: PostgreSQL connection string
- mongodb_uri: MongoDB connection URI
- redis_url: Redis connection URL

### Authentification (secret/auth)
- jwt_secret: JWT signing secret
- jwt_refresh_secret: JWT refresh token secret
- session_secret: Session encryption secret

### APIs (secret/api)
- openai_key: OpenAI API key (placeholder)
- stripe_key: Stripe API key (placeholder)
- sendgrid_key: SendGrid API key (placeholder)

### Sécurité (secret/security)
- encryption_key: Application encryption key
- hash_salt: Password hashing salt

### Monitoring (secret/monitoring)
- sentry_dsn: Sentry DSN (placeholder)

## Fichiers Générés

- \`.env.vault\`: Configuration Vault
- \`scripts/load-vault-secrets.sh\`: Script de chargement des secrets
- \`scripts/test-vault.sh\`: Script de test

## Utilisation

### Charger les secrets:
\`\`\`bash
source ./scripts/load-vault-secrets.sh
\`\`\`

### Tester Vault:
\`\`\`bash
./scripts/test-vault.sh
\`\`\`

### Lister les secrets:
\`\`\`bash
export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"
vault kv list secret/
\`\`\`

### Récupérer un secret:
\`\`\`bash
vault kv get secret/auth
\`\`\`

## Sécurité

⚠️ **ATTENTION**: 
- Configuration en mode développement uniquement
- Token root utilisé (dev-token)
- En production, utilisez des tokens avec permissions limitées
- Configurez TLS et authentification appropriée

## Prochaines Étapes

1. Tester l'accès aux secrets
2. Mettre à jour les applications pour utiliser Vault
3. Remplacer les secrets hardcodés
4. Configurer les politiques d'accès (production)
5. Implémenter la rotation des secrets

EOF

# Sauvegarder le PID de Vault
echo "$VAULT_PID" > "$VAULT_DIR/vault.pid"

# Test final
log "Test final de Vault..."
if ./scripts/test-vault.sh > "$SECRETS_BACKUP_DIR/test-results.txt" 2>&1; then
    success "✅ Test de Vault réussi"
else
    warning "⚠️ Test de Vault avec avertissements"
fi

# Résumé final
echo ""
echo "🎯 RÉSUMÉ DE LA CONFIGURATION VAULT"
echo "==================================="
echo "📊 Secrets créés: 5 groupes (database, auth, api, security, monitoring)"
echo "📁 Rapport: $SECRETS_BACKUP_DIR/vault-setup-report.md"
echo "🔧 Scripts: load-vault-secrets.sh, test-vault.sh"
echo "🔑 PID Vault: $VAULT_PID (sauvé dans vault-config/vault.pid)"
echo ""

success "🎉 CONFIGURATION VAULT COMPLÉTÉE!"
echo ""
echo "🔄 Prochaines étapes:"
echo "  1. Tester: ./scripts/test-vault.sh"
echo "  2. Charger: source ./scripts/load-vault-secrets.sh"
echo "  3. Continuer avec la configuration WAF"
echo ""
echo "ℹ️  Pour arrêter Vault: kill \$(cat vault-config/vault.pid)"
EOF
