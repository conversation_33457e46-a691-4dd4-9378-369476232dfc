#!/bin/bash

# 🚀 SCRIPT D'AUTOMATISATION EXCELLENCE 10/10
# Automatise l'implémentation du plan d'action pour atteindre l'excellence absolue

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="${PROJECT_ROOT}/logs/excellence-automation.log"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
    esac
}

# Vérification des prérequis
check_prerequisites() {
    log "INFO" "🔍 Vérification des prérequis..."
    
    local missing_tools=()
    
    # Outils requis
    local required_tools=("docker" "kubectl" "node" "npm" "git" "curl" "jq")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log "ERROR" "Outils manquants: ${missing_tools[*]}"
        log "INFO" "Installez les outils manquants avant de continuer"
        exit 1
    fi
    
    log "INFO" "✅ Tous les prérequis sont satisfaits"
}

# Phase 1: Infrastructure Production
setup_production_infrastructure() {
    log "INFO" "🏗️ Phase 1: Configuration Infrastructure Production"
    
    # 1. Optimisation Docker
    log "INFO" "📦 Optimisation des Dockerfiles..."
    
    # Création du Dockerfile optimisé pour le backend
    cat > "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS/Dockerfile.production" << 'EOF'
# Multi-stage build pour optimisation
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS runtime

# Sécurité: utilisateur non-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

WORKDIR /app

# Copie des dépendances
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --chown=nestjs:nodejs . .

# Build de l'application
RUN npm run build

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

USER nestjs

EXPOSE 3000

CMD ["node", "dist/main"]
EOF
    
    # 2. Manifests Kubernetes
    log "INFO" "☸️ Génération des manifests Kubernetes..."
    
    mkdir -p "${PROJECT_ROOT}/k8s/production"
    
    # HorizontalPodAutoscaler
    cat > "${PROJECT_ROOT}/k8s/production/hpa.yaml" << 'EOF'
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: retreat-and-be-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: retreat-and-be-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
EOF
    
    # PodDisruptionBudget
    cat > "${PROJECT_ROOT}/k8s/production/pdb.yaml" << 'EOF'
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: retreat-and-be-pdb
  namespace: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: retreat-and-be-backend
EOF
    
    # NetworkPolicy
    cat > "${PROJECT_ROOT}/k8s/production/network-policy.yaml" << 'EOF'
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: retreat-and-be-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: retreat-and-be-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
EOF
    
    log "INFO" "✅ Infrastructure production configurée"
}

# Phase 2: Tests E2E
setup_e2e_testing() {
    log "INFO" "🧪 Phase 2: Configuration Tests E2E"
    
    cd "${PROJECT_ROOT}/Front-Audrey-V1-Main-main"
    
    # Installation des outils de test
    log "INFO" "📦 Installation des frameworks de test..."
    npm install --save-dev cypress @playwright/test @testing-library/cypress
    
    # Configuration Cypress
    mkdir -p cypress/{e2e,fixtures,support}
    
    cat > cypress.config.js << 'EOF'
const { defineConfig } = require('cypress')

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    setupNodeEvents(on, config) {
      // Configuration des plugins
    },
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'webpack',
    },
  },
})
EOF
    
    # Test critique: Parcours utilisateur complet
    cat > cypress/e2e/user-journey-complete.cy.js << 'EOF'
describe('Parcours Utilisateur Complet', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('Inscription → Connexion → Réservation → Paiement', () => {
    // 1. Inscription
    cy.get('[data-cy=signup-button]').click()
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('SecurePassword123!')
    cy.get('[data-cy=confirm-password-input]').type('SecurePassword123!')
    cy.get('[data-cy=signup-submit]').click()
    
    // Vérification email de confirmation
    cy.contains('Vérifiez votre email').should('be.visible')
    
    // 2. Connexion (simulation email confirmé)
    cy.visit('/login')
    cy.get('[data-cy=email-input]').type('<EMAIL>')
    cy.get('[data-cy=password-input]').type('SecurePassword123!')
    cy.get('[data-cy=login-submit]').click()
    
    // Vérification connexion réussie
    cy.url().should('include', '/dashboard')
    cy.get('[data-cy=user-menu]').should('be.visible')
    
    // 3. Recherche et réservation
    cy.get('[data-cy=search-input]').type('Yoga Paris')
    cy.get('[data-cy=search-button]').click()
    
    // Sélection d'une activité
    cy.get('[data-cy=activity-card]').first().click()
    cy.get('[data-cy=book-button]').click()
    
    // Sélection date et heure
    cy.get('[data-cy=date-picker]').click()
    cy.get('[data-cy=available-date]').first().click()
    cy.get('[data-cy=time-slot]').first().click()
    cy.get('[data-cy=confirm-booking]').click()
    
    // 4. Paiement
    cy.get('[data-cy=payment-method]').select('card')
    cy.get('[data-cy=card-number]').type('****************')
    cy.get('[data-cy=card-expiry]').type('12/25')
    cy.get('[data-cy=card-cvc]').type('123')
    cy.get('[data-cy=pay-button]').click()
    
    // Vérification confirmation
    cy.contains('Réservation confirmée').should('be.visible')
    cy.get('[data-cy=booking-reference]').should('be.visible')
  })

  it('Test Responsive Mobile', () => {
    cy.viewport('iphone-x')
    
    // Navigation mobile
    cy.get('[data-cy=mobile-menu-button]').click()
    cy.get('[data-cy=mobile-menu]').should('be.visible')
    
    // Recherche mobile
    cy.get('[data-cy=mobile-search]').type('Méditation')
    cy.get('[data-cy=mobile-search-button]').click()
    
    // Vérification affichage mobile
    cy.get('[data-cy=activity-card]').should('be.visible')
    cy.get('[data-cy=activity-card]').should('have.css', 'width')
  })

  it('Test Accessibilité WCAG 2.1', () => {
    // Vérification des contrastes
    cy.get('body').should('have.css', 'color')
    
    // Navigation au clavier
    cy.get('body').tab()
    cy.focused().should('have.attr', 'data-cy', 'main-navigation')
    
    // Attributs ARIA
    cy.get('[role=button]').should('exist')
    cy.get('[aria-label]').should('exist')
    
    // Images avec alt text
    cy.get('img').each(($img) => {
      cy.wrap($img).should('have.attr', 'alt')
    })
  })
})
EOF
    
    log "INFO" "✅ Tests E2E configurés"
}

# Phase 3: Optimisations Performance
optimize_performance() {
    log "INFO" "⚡ Phase 3: Optimisations Performance"
    
    # Optimisation base de données
    log "INFO" "🗄️ Optimisation base de données..."
    
    cat > "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS/database/performance-optimizations.sql" << 'EOF'
-- Index pour les requêtes fréquentes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_activity_id ON bookings(activity_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_category ON activities(category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_location ON activities(location);

-- Index composites pour les recherches complexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_category_location 
ON activities(category, location);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bookings_user_date 
ON bookings(user_id, booking_date);

-- Optimisation des requêtes de recommandations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_preferences_user_id 
ON user_preferences(user_id);

-- Statistiques pour l'optimiseur
ANALYZE users;
ANALYZE bookings;
ANALYZE activities;
ANALYZE user_preferences;
EOF
    
    # Configuration Redis avancée
    cat > "${PROJECT_ROOT}/Projet-RB2/Backend-NestJS/config/redis.config.ts" << 'EOF'
import { CacheModuleOptions } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';

export const redisConfig: CacheModuleOptions = {
  store: redisStore,
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB) || 0,
  ttl: 3600, // 1 heure par défaut
  max: 1000, // Maximum 1000 clés en cache
  
  // Configuration avancée
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('Redis server connection refused');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('Retry time exhausted');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    return Math.min(options.attempt * 100, 3000);
  },
  
  // Pool de connexions
  family: 4,
  keepAlive: true,
  lazyConnect: true,
};
EOF
    
    # Optimisation frontend
    log "INFO" "🎨 Optimisation frontend..."
    
    cd "${PROJECT_ROOT}/Front-Audrey-V1-Main-main"
    
    # Configuration Webpack optimisée
    cat > webpack.config.production.js << 'EOF'
const path = require('path');
const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  mode: 'production',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].[contenthash].chunk.js',
    clean: true,
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      }),
    ],
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    },
  },
  plugins: [
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
    }),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production'),
    }),
    // Décommentez pour analyser le bundle
    // new BundleAnalyzerPlugin(),
  ],
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { targets: 'defaults' }],
              '@babel/preset-react',
              '@babel/preset-typescript',
            ],
            plugins: [
              '@babel/plugin-syntax-dynamic-import',
              'react-lazy-load',
            ],
          },
        },
      },
    ],
  },
};
EOF
    
    log "INFO" "✅ Optimisations performance appliquées"
}

# Phase 4: Monitoring et Documentation
setup_monitoring_docs() {
    log "INFO" "📊 Phase 4: Monitoring et Documentation"
    
    # Configuration Prometheus
    mkdir -p "${PROJECT_ROOT}/monitoring"
    
    cat > "${PROJECT_ROOT}/monitoring/prometheus.yml" << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'retreat-and-be-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'retreat-and-be-frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
EOF
    
    # Règles d'alerting
    cat > "${PROJECT_ROOT}/monitoring/alert_rules.yml" << 'EOF'
groups:
  - name: retreat-and-be-alerts
    rules:
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 0.2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Temps de réponse élevé détecté"
          description: "Le temps de réponse P95 est supérieur à 200ms pendant 5 minutes"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Taux d'erreur élevé"
          description: "Le taux d'erreur 5xx est supérieur à 1% pendant 2 minutes"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Nombre élevé de connexions à la base de données"
          description: "Plus de 80 connexions actives à la base de données"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Utilisation mémoire Redis élevée"
          description: "Redis utilise plus de 90% de sa mémoire allouée"
EOF
    
    log "INFO" "✅ Monitoring configuré"
}

# Fonction principale
main() {
    log "INFO" "🚀 Démarrage de l'automatisation Excellence 10/10"
    log "INFO" "📁 Répertoire projet: $PROJECT_ROOT"
    
    # Création du répertoire de logs
    mkdir -p "${PROJECT_ROOT}/logs"
    
    # Vérification des prérequis
    check_prerequisites
    
    # Exécution des phases
    setup_production_infrastructure
    setup_e2e_testing
    optimize_performance
    setup_monitoring_docs
    
    log "INFO" "🎉 Automatisation Excellence 10/10 terminée avec succès!"
    log "INFO" "📋 Consultez le fichier de log: $LOG_FILE"
    log "INFO" "📖 Suivez le plan d'action détaillé dans PLAN_ACTION_EXCELLENCE_10_10.md"
}

# Gestion des erreurs
trap 'log "ERROR" "Script interrompu à la ligne $LINENO"' ERR

# Exécution
main "$@"
