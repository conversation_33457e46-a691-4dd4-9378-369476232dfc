import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Métriques personnalisées pour excellence 10/10
const errorRate = new Rate('error_rate');
const responseTime = new Trend('response_time');
const throughput = new Counter('requests_total');

// Configuration des tests de charge pour excellence 10/10
export const options = {
  stages: [
    // Montée en charge progressive
    { duration: '2m', target: 100 },   // Montée à 100 utilisateurs
    { duration: '3m', target: 500 },   // Montée à 500 utilisateurs
    { duration: '5m', target: 1000 },  // Montée à 1000 utilisateurs
    { duration: '5m', target: 2000 },  // Pic à 2000 utilisateurs (cible excellence)
    { duration: '3m', target: 2000 },  // Maintien du pic
    { duration: '2m', target: 0 },     // Descente progressive
  ],
  
  thresholds: {
    // Critères d'excellence 10/10
    http_req_duration: ['p(95)<150'], // 95% des requêtes < 150ms
    http_req_rate: ['rate>2000'],     // > 2000 req/sec
    http_req_failed: ['rate<0.01'],   // < 1% d'erreurs
    error_rate: ['rate<0.01'],        // < 1% d'erreurs personnalisées
    response_time: ['p(99)<500'],     // 99% < 500ms
  },
  
  // Configuration avancée
  noConnectionReuse: false,
  userAgent: 'K6-Excellence-Test/1.0',
  insecureSkipTLSVerify: true,
  
  // Limites de ressources
  maxRedirects: 4,
  batch: 20,
  batchPerHost: 10,
};

// Configuration de l'environnement
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_URL = `${BASE_URL}/api`;

// Données de test
const testUsers = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

const searchTerms = [
  'yoga paris',
  'méditation lyon',
  'pilates marseille',
  'massage toulouse',
  'retraite spirituelle',
  'bien-être bordeaux',
];

// Fonction utilitaire pour les headers
function getHeaders(token = null) {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'K6-Excellence-Test/1.0',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

// Test de connexion utilisateur
function loginUser() {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });
  
  const response = http.post(`${API_URL}/auth/login`, loginPayload, {
    headers: getHeaders(),
    tags: { endpoint: 'login' },
  });
  
  const success = check(response, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 200ms': (r) => r.timings.duration < 200,
    'login has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.access_token !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  if (success && response.status === 200) {
    try {
      const body = JSON.parse(response.body);
      return body.access_token;
    } catch {
      return null;
    }
  }
  
  return null;
}

// Test de recherche d'activités
function searchActivities(token) {
  const searchTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];
  
  const params = {
    q: searchTerm,
    limit: 20,
    offset: 0,
  };
  
  const url = `${API_URL}/activities/search?${Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')}`;
  
  const response = http.get(url, {
    headers: getHeaders(token),
    tags: { endpoint: 'search' },
  });
  
  const success = check(response, {
    'search status is 200': (r) => r.status === 200,
    'search response time < 150ms': (r) => r.timings.duration < 150,
    'search has results': (r) => {
      try {
        const body = JSON.parse(r.body);
        return Array.isArray(body.data) && body.data.length > 0;
      } catch {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  if (success && response.status === 200) {
    try {
      const body = JSON.parse(response.body);
      return body.data;
    } catch {
      return [];
    }
  }
  
  return [];
}

// Test de récupération des détails d'une activité
function getActivityDetails(activityId, token) {
  const response = http.get(`${API_URL}/activities/${activityId}`, {
    headers: getHeaders(token),
    tags: { endpoint: 'activity-details' },
  });
  
  const success = check(response, {
    'activity details status is 200': (r) => r.status === 200,
    'activity details response time < 100ms': (r) => r.timings.duration < 100,
    'activity details has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  return success;
}

// Test de récupération des recommandations
function getRecommendations(token) {
  const response = http.get(`${API_URL}/recommendations`, {
    headers: getHeaders(token),
    tags: { endpoint: 'recommendations' },
  });
  
  const success = check(response, {
    'recommendations status is 200': (r) => r.status === 200,
    'recommendations response time < 200ms': (r) => r.timings.duration < 200,
    'recommendations has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return Array.isArray(body.recommendations);
      } catch {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  return success;
}

// Test de création de réservation
function createBooking(activityId, token) {
  const bookingPayload = JSON.stringify({
    activity_id: activityId,
    booking_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // +7 jours
    time_slot: '10:00',
    participants: 1,
  });
  
  const response = http.post(`${API_URL}/bookings`, bookingPayload, {
    headers: getHeaders(token),
    tags: { endpoint: 'create-booking' },
  });
  
  const success = check(response, {
    'booking creation status is 201': (r) => r.status === 201,
    'booking creation response time < 300ms': (r) => r.timings.duration < 300,
    'booking creation has id': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  return success;
}

// Test de health check
function healthCheck() {
  const response = http.get(`${API_URL}/health`, {
    headers: getHeaders(),
    tags: { endpoint: 'health' },
  });
  
  const success = check(response, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 50ms': (r) => r.timings.duration < 50,
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  throughput.add(1);
  
  return success;
}

// Scénario principal de test
export default function () {
  // 1. Health check (10% des requêtes)
  if (Math.random() < 0.1) {
    healthCheck();
    sleep(0.5);
    return;
  }
  
  // 2. Connexion utilisateur
  const token = loginUser();
  if (!token) {
    sleep(1);
    return;
  }
  
  sleep(0.5);
  
  // 3. Recherche d'activités (80% des utilisateurs)
  if (Math.random() < 0.8) {
    const activities = searchActivities(token);
    
    if (activities.length > 0) {
      sleep(0.5);
      
      // 4. Consultation des détails (60% des recherches)
      if (Math.random() < 0.6) {
        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        getActivityDetails(randomActivity.id, token);
        
        sleep(0.5);
        
        // 5. Création de réservation (20% des consultations)
        if (Math.random() < 0.2) {
          createBooking(randomActivity.id, token);
        }
      }
    }
  }
  
  // 6. Récupération des recommandations (40% des utilisateurs)
  if (Math.random() < 0.4) {
    getRecommendations(token);
  }
  
  // Pause entre les actions
  sleep(Math.random() * 2 + 1); // 1-3 secondes
}

// Test de setup (exécuté une fois au début)
export function setup() {
  console.log('🚀 Démarrage des tests de performance Excellence 10/10');
  console.log(`📊 URL de base: ${BASE_URL}`);
  console.log(`🎯 Objectif: >2000 req/sec avec <150ms P95`);
  
  // Vérification de la disponibilité de l'API
  const response = http.get(`${API_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`API non disponible: ${response.status}`);
  }
  
  return { startTime: Date.now() };
}

// Test de teardown (exécuté une fois à la fin)
export function teardown(data) {
  const duration = (Date.now() - data.startTime) / 1000;
  console.log(`✅ Tests terminés en ${duration}s`);
  console.log('📈 Consultez les métriques pour validation Excellence 10/10');
}

// Gestion des erreurs
export function handleSummary(data) {
  const summary = {
    timestamp: new Date().toISOString(),
    test_duration: data.state.testRunDurationMs / 1000,
    total_requests: data.metrics.http_reqs.values.count,
    request_rate: data.metrics.http_reqs.values.rate,
    avg_response_time: data.metrics.http_req_duration.values.avg,
    p95_response_time: data.metrics.http_req_duration.values['p(95)'],
    p99_response_time: data.metrics.http_req_duration.values['p(99)'],
    error_rate: data.metrics.http_req_failed.values.rate * 100,
    
    // Validation des critères d'excellence
    excellence_criteria: {
      request_rate_target: data.metrics.http_reqs.values.rate >= 2000,
      p95_response_time_target: data.metrics.http_req_duration.values['p(95)'] <= 150,
      error_rate_target: data.metrics.http_req_failed.values.rate <= 0.01,
    },
  };
  
  // Calcul du score d'excellence
  const criteriaCount = Object.values(summary.excellence_criteria).filter(Boolean).length;
  const totalCriteria = Object.keys(summary.excellence_criteria).length;
  summary.excellence_score = (criteriaCount / totalCriteria) * 100;
  
  console.log('\n🏆 RÉSULTATS EXCELLENCE 10/10:');
  console.log(`📊 Taux de requêtes: ${summary.request_rate.toFixed(2)} req/sec (cible: >2000)`);
  console.log(`⚡ Temps de réponse P95: ${summary.p95_response_time.toFixed(2)}ms (cible: <150ms)`);
  console.log(`🎯 Taux d'erreur: ${summary.error_rate.toFixed(2)}% (cible: <1%)`);
  console.log(`🏅 Score d'excellence: ${summary.excellence_score.toFixed(1)}%`);
  
  return {
    'performance-report.json': JSON.stringify(summary, null, 2),
    stdout: `Excellence Score: ${summary.excellence_score.toFixed(1)}%`,
  };
}
