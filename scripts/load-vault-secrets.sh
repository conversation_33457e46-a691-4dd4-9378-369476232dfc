#!/bin/bash

# 🔐 CHARGEMENT DES SECRETS DEPUIS VAULT
# Utilisation: source ./scripts/load-vault-secrets.sh

export VAULT_ADDR="http://127.0.0.1:8200"
export VAULT_TOKEN="dev-token"

# Fonction pour récupérer un secret
get_secret() {
    local path="$1"
    local key="$2"
    vault kv get -field="$key" "secret/$path" 2>/dev/null
}

# Charger les secrets dans les variables d'environnement
log() {
    echo -e "\033[0;34m[VAULT]\033[0m $1"
}

log "Chargement des secrets depuis Vault..."

export DATABASE_URL=$(get_secret "database" "url")
export MONGODB_URI=$(get_secret "database" "mongodb_uri")
export REDIS_URL=$(get_secret "database" "redis_url")

export JWT_SECRET=$(get_secret "auth" "jwt_secret")
export JWT_REFRESH_SECRET=$(get_secret "auth" "jwt_refresh_secret")
export SESSION_SECRET=$(get_secret "auth" "session_secret")

export API_KEY_OPENAI=$(get_secret "api" "openai_key")
export API_KEY_STRIPE=$(get_secret "api" "stripe_key")
export API_KEY_SENDGRID=$(get_secret "api" "sendgrid_key")

export ENCRYPTION_KEY=$(get_secret "security" "encryption_key")
export HASH_SALT=$(get_secret "security" "hash_salt")

export SENTRY_DSN=$(get_secret "monitoring" "sentry_dsn")

log "✅ Secrets chargés avec succès"
log "📊 Variables disponibles: DATABASE_URL, JWT_SECRET, API_KEY_OPENAI, etc."
