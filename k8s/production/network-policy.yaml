apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: retreat-and-be-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: retreat-and-be-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
