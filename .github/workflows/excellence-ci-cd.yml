name: Excellence 10/10 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: retreat-and-be

jobs:
  # =====================================================
  # PHASE 1: TESTS ET QUALITÉ
  # =====================================================
  test-backend:
    name: 🧪 Tests Backend
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: retreat_and_be_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Projet-RB2/Backend-NestJS/package-lock.json'
      
      - name: 📦 Install dependencies
        working-directory: Projet-RB2/Backend-NestJS
        run: npm ci
      
      - name: 🔍 Lint code
        working-directory: Projet-RB2/Backend-NestJS
        run: npm run lint
      
      - name: 🧪 Run unit tests
        working-directory: Projet-RB2/Backend-NestJS
        run: npm run test:cov
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/retreat_and_be_test
          REDIS_URL: redis://localhost:6379
      
      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./Projet-RB2/Backend-NestJS/coverage/lcov.info
          flags: backend
          name: backend-coverage

  test-frontend:
    name: 🎨 Tests Frontend
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Projet-RB2/Front-Audrey-V1-Main-main/package-lock.json'
      
      - name: 📦 Install dependencies
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm ci
      
      - name: 🔍 Lint code
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm run lint
      
      - name: 🧪 Run unit tests
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm run test:coverage
      
      - name: 🏗️ Build application
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm run build
      
      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./Projet-RB2/Front-Audrey-V1-Main-main/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # =====================================================
  # PHASE 2: TESTS E2E
  # =====================================================
  e2e-tests:
    name: 🎭 Tests E2E
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Projet-RB2/Front-Audrey-V1-Main-main/package-lock.json'
      
      - name: 📦 Install dependencies
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm ci
      
      - name: 🎭 Run Cypress E2E tests
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npx cypress run --config-file cypress.config.production.js
        env:
          CYPRESS_baseUrl: http://localhost:3000
      
      - name: 📸 Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: Projet-RB2/Front-Audrey-V1-Main-main/cypress/screenshots

  # =====================================================
  # PHASE 3: SÉCURITÉ
  # =====================================================
  security-scan:
    name: 🔒 Scan de sécurité
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🔍 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: 📊 Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: 🔍 Run npm audit (Backend)
        working-directory: Projet-RB2/Backend-NestJS
        run: npm audit --audit-level high
      
      - name: 🔍 Run npm audit (Frontend)
        working-directory: Projet-RB2/Front-Audrey-V1-Main-main
        run: npm audit --audit-level high

  # =====================================================
  # PHASE 4: PERFORMANCE
  # =====================================================
  performance-tests:
    name: ⚡ Tests de performance
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🚀 Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
      
      - name: ⚡ Run performance tests
        run: k6 run scripts/performance-tests-k6.js
        env:
          BASE_URL: http://localhost:3000
      
      - name: 📊 Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-report.json

  # =====================================================
  # PHASE 5: BUILD ET DÉPLOIEMENT
  # =====================================================
  build-and-push:
    name: 🏗️ Build & Push Docker Images
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        service: [backend, frontend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: 🔑 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: 📝 Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: 🏗️ Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./Projet-RB2/${{ matrix.service == 'backend' && 'Backend-NestJS' || 'Front-Audrey-V1-Main-main' }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # =====================================================
  # PHASE 6: DÉPLOIEMENT STAGING
  # =====================================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: ⚙️ Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: 🔑 Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
      
      - name: 🚀 Deploy to staging
        run: |
          export KUBECONFIG=kubeconfig
          kubectl set image deployment/retreat-and-be-backend backend=${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/backend:${{ github.sha }} -n staging
          kubectl set image deployment/retreat-and-be-frontend frontend=${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/frontend:${{ github.sha }} -n staging
          kubectl rollout status deployment/retreat-and-be-backend -n staging
          kubectl rollout status deployment/retreat-and-be-frontend -n staging
      
      - name: 🧪 Run smoke tests
        run: |
          sleep 30
          curl -f https://staging-api.retreat-and-be.com/health || exit 1
          curl -f https://staging.retreat-and-be.com || exit 1

  # =====================================================
  # PHASE 7: VALIDATION EXCELLENCE 10/10
  # =====================================================
  validate-excellence:
    name: 🏆 Validation Excellence 10/10
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 🏆 Run excellence validation
        run: |
          chmod +x scripts/validate-excellence-10-10.sh
          ./scripts/validate-excellence-10-10.sh
      
      - name: 📊 Upload excellence report
        uses: actions/upload-artifact@v3
        with:
          name: excellence-report
          path: EXCELLENCE_VALIDATION_REPORT_*.md
      
      - name: 💬 Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const reportFiles = fs.readdirSync('.').filter(f => f.startsWith('EXCELLENCE_VALIDATION_REPORT_'));
            if (reportFiles.length > 0) {
              const report = fs.readFileSync(reportFiles[0], 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `## 🏆 Rapport d'Excellence 10/10\n\n\`\`\`\n${report}\n\`\`\``
              });
            }

  # =====================================================
  # PHASE 8: DÉPLOIEMENT PRODUCTION (MANUEL)
  # =====================================================
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [validate-excellence]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: ⚙️ Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: 🔑 Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
      
      - name: 🌟 Deploy to production
        run: |
          export KUBECONFIG=kubeconfig
          kubectl set image deployment/retreat-and-be-backend backend=${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/backend:${{ github.sha }} -n production
          kubectl set image deployment/retreat-and-be-frontend frontend=${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/frontend:${{ github.sha }} -n production
          kubectl rollout status deployment/retreat-and-be-backend -n production
          kubectl rollout status deployment/retreat-and-be-frontend -n production
      
      - name: 🎉 Notify deployment success
        run: |
          echo "🎉 Déploiement en production réussi!"
          echo "🏆 Excellence 10/10 maintenue!"
