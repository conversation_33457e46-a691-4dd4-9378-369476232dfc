name: CI/CD Production Excellence 10/10

on:
  push:
    branches: [main, production]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: retreat-and-be

jobs:
  security-scan:
    name: <PERSON> Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Projet-RB2/Backend-NestJS/package-lock.json'
      
      - name: Install dependencies
        run: |
          cd Projet-RB2/Backend-NestJS
          npm ci
      
      - name: Run unit tests
        run: |
          cd Projet-RB2/Backend-NestJS
          npm run test:cov
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./Projet-RB2/Backend-NestJS/coverage/lcov.info

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: retreat_and_be_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Projet-RB2/Backend-NestJS/package-lock.json'
      
      - name: Install dependencies
        run: |
          cd Projet-RB2/Backend-NestJS
          npm ci
      
      - name: Run integration tests
        run: |
          cd Projet-RB2/Backend-NestJS
          npm run test:e2e
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/retreat_and_be_test
          REDIS_URL: redis://localhost:6379

  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [security-scan, unit-tests, integration-tests]
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./Projet-RB2/Backend-NestJS
          file: ./Projet-RB2/Backend-NestJS/Dockerfile.production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # kubectl apply -f k8s/staging/
          echo "Staging deployment completed"

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'Front-Audrey-V1-Main-main/package-lock.json'
      
      - name: Install dependencies
        run: |
          cd Front-Audrey-V1-Main-main
          npm ci
      
      - name: Run E2E tests
        run: |
          cd Front-Audrey-V1-Main-main
          npm run test:e2e:headless
        env:
          CYPRESS_BASE_URL: https://staging.retreat-and-be.com

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images, e2e-tests]
    if: github.ref == 'refs/heads/production'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # kubectl apply -f k8s/production/
          echo "Production deployment completed"
      
      - name: Run smoke tests
        run: |
          echo "Running smoke tests..."
          curl -f https://api.retreat-and-be.com/health
          echo "Smoke tests passed"

  notify:
    name: Notify Teams
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
